import { Component, OnInit, OnDestroy, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IconsModule } from '../../../icons/icons.module';
import { TranslateModule } from '@ngx-translate/core';
import { AdminPanelService } from '../../../core/services/admin-panel.service';
import { AdminPanelTenant } from '../../../model/response/admin-panel-tenant.model';
import { ToastService } from '../../../core/services/toast.service';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-panels-management',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    IconsModule,
    TranslateModule
  ],
  templateUrl: './panels-management.component.html',
  styleUrls: ['./panels-management.component.css']
})
export class PanelsManagementComponent implements OnInit, OnD<PERSON>roy {
  // Make Math available in template
  Math = Math;

  @Output() openDomainInfo = new EventEmitter<{tenantId: string, domain: string}>();

  // Panels data
  panels: AdminPanelTenant[] = [];
  searchTerm: string = '';
  pagination = {
    page_number: 0,
    page_size: 10,
    total_elements: 0,
    total_pages: 0
  };

  // Loading state
  isLoading = false;

  private subscriptions: Subscription[] = [];

  constructor(
    private adminPanelService: AdminPanelService,
    private toastService: ToastService
  ) {}

  ngOnInit() {
    // Subscribe to panels data
    this.subscriptions.push(
      this.adminPanelService.panels$.subscribe(panels => {
        this.panels = panels;
      })
    );

    // Subscribe to loading state
    this.subscriptions.push(
      this.adminPanelService.loading$.subscribe(loading => {
        this.isLoading = loading;
      })
    );

    // Subscribe to panels pagination
    this.subscriptions.push(
      this.adminPanelService.panelsPagination$.subscribe(pagination => {
        this.pagination = pagination;
      })
    );

    // Load initial data
    this.loadPanels();
  }

  ngOnDestroy() {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  // Panels methods
  loadPanels(page: number = 0) {
    this.adminPanelService.getAllPanels(page, this.pagination.page_size, this.searchTerm)
      .subscribe({
        error: (error) => {
          console.error('Error loading panels:', error);
          this.toastService.showError('Failed to load panels');
        }
      });
  }

  search() {
    this.loadPanels(0);
  }

  resetSearch() {
    this.searchTerm = '';
    this.loadPanels(0);
  }

  // Pagination methods
  loadPage(page: number) {
    this.loadPanels(page);
  }

  // Domain info modal
  onOpenDomainInfo(tenantId: string, domain: string) {
    this.openDomainInfo.emit({ tenantId, domain });
  }

  // Utility methods
  formatDate(dateString: string): string {
    if (!dateString) return '-';
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
  }

  getStatusClass(status: string): string {
    switch (status?.toLowerCase()) {
      case 'active':
      case 'activated':
        return 'bg-green-100 text-green-800';
      case 'suspended':
      case 'deactivated':
        return 'bg-red-100 text-red-800';
      case 'expired':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  }
}
