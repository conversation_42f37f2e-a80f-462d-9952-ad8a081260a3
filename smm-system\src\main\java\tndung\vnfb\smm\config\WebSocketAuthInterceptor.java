package tndung.vnfb.smm.config;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.simp.stomp.StompCommand;
import org.springframework.messaging.simp.stomp.StompHeaderAccessor;
import org.springframework.messaging.support.ChannelInterceptor;
import org.springframework.messaging.support.MessageHeaderAccessor;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.stereotype.Component;
import tndung.vnfb.smm.service.TokenProvider;
import tndung.vnfb.smm.service.KeyTokenService;
import tndung.vnfb.smm.entity.KeyToken;

import java.security.Principal;
import java.util.List;

@Component
@RequiredArgsConstructor
@Slf4j
public class WebSocketAuthInterceptor implements ChannelInterceptor {

    private final TokenProvider tokenProvider;
    private final KeyTokenService keyTokenService;
    private final UserDetailsService userDetailsService;

    @Override
    public Message<?> preSend(Message<?> message, MessageChannel channel) {
        StompHeaderAccessor accessor = MessageHeaderAccessor.getAccessor(message, StompHeaderAccessor.class);

        if (accessor != null && StompCommand.CONNECT.equals(accessor.getCommand())) {
            log.info("=== WebSocket CONNECT command received ===");

            // Get token and client ID from headers
            List<String> authHeaders = accessor.getNativeHeader("Authorization");
            List<String> clientIdHeaders = accessor.getNativeHeader("x-client-id");

            log.info("Authorization headers: {}", authHeaders);
            log.info("Client ID headers: {}", clientIdHeaders);

            if (authHeaders != null && !authHeaders.isEmpty() &&
                clientIdHeaders != null && !clientIdHeaders.isEmpty()) {

                String authHeader = authHeaders.get(0);
                String clientId = clientIdHeaders.get(0);

                if (authHeader != null && authHeader.startsWith("Bearer ") && clientId != null) {
                    String token = authHeader.substring(7);

                    try {
                        log.info("Attempting WebSocket authentication with token and client ID: {}", clientId);

                        // Get key token for validation
                        KeyToken keyToken = keyTokenService.findByIdAndSite(clientId);
                        if (keyToken == null) {
                            log.warn("KeyToken not found for client ID: {}", clientId);
                            return message;
                        }

                        // Get username from token
                        String username = tokenProvider.getSub(keyToken.getPublicKey(), token);
                        if (username == null || username.isEmpty()) {
                            log.warn("Username not found in token");
                            return message;
                        }

                        // Validate token
                        UserDetails userDetails = userDetailsService.loadUserByUsername(username);
                        if (tokenProvider.validateToken(keyToken.getPublicKey(), token, userDetails)) {
                            // Get actual user ID from UserDetails
                            final String actualUserId;
                            if (userDetails instanceof tndung.vnfb.smm.dto.CustomUserDetails) {
                                tndung.vnfb.smm.dto.CustomUserDetails customUserDetails = (tndung.vnfb.smm.dto.CustomUserDetails) userDetails;
                                actualUserId = customUserDetails.getUser().getId().toString();
                            } else {
                                actualUserId = null;
                            }

                            if (actualUserId != null) {
                                // Set user principal with actual user ID for WebSocket session
                                Principal principal = () -> actualUserId;
                                accessor.setUser(principal);

                                log.info("WebSocket authentication successful for username: {} -> user ID: {}", username, actualUserId);
                                log.info("User principal set: {}", principal.getName());
                            } else {
                                log.warn("Could not extract user ID from UserDetails for username: {}", username);
                                // Fallback to username
                                Principal principal = () -> username;
                                accessor.setUser(principal);
                                log.info("WebSocket authentication fallback - using username: {}", username);
                            }
                        } else {
                            log.warn("Token validation failed for username: {}", username);
                        }

                    } catch (Exception e) {
                        log.error("WebSocket authentication failed: {}", e.getMessage(), e);
                    }
                }
            } else {
                // For SockJS, authentication might be handled differently
                log.info("No Authorization header or Client ID found, allowing connection for SockJS");
            }
        }

        return message;
    }
}
