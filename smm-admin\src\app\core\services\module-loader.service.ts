import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, of } from 'rxjs';
import { map, shareReplay, tap } from 'rxjs/operators';

/**
 * Service to manage module loading and prevent duplicate bundle loading
 * This service tracks loaded modules and provides caching mechanism
 */
@Injectable({
  providedIn: 'root'
})
export class ModuleLoaderService {
  private loadedModules = new Map<string, any>();
  private loadingModules = new Map<string, Observable<any>>();
  private loadingState = new BehaviorSubject<{[key: string]: boolean}>({});

  constructor() {
    // Log module loading for debugging
    if (typeof window !== 'undefined' && (window as any)['ngDevMode']) {
      console.log('🔧 ModuleLoaderService initialized');
    }
  }

  /**
   * Load a component with caching to prevent duplicate loading
   */
  loadComponent<T>(
    moduleKey: string, 
    loader: () => Promise<any>
  ): Observable<T> {
    // Check if module is already loaded
    if (this.loadedModules.has(moduleKey)) {
      return of(this.loadedModules.get(moduleKey));
    }

    // Check if module is currently being loaded
    if (this.loadingModules.has(moduleKey)) {
      return this.loadingModules.get(moduleKey)!;
    }

    // Start loading the module
    this.updateLoadingState(moduleKey, true);
    
    const loading$ = new Observable<T>(subscriber => {
      loader()
        .then(module => {
          // Cache the loaded module
          this.loadedModules.set(moduleKey, module);
          this.updateLoadingState(moduleKey, false);
          
          if (typeof window !== 'undefined' && (window as any)['ngDevMode']) {
            console.log(`✅ Module loaded: ${moduleKey}`);
          }
          
          subscriber.next(module);
          subscriber.complete();
        })
        .catch(error => {
          this.updateLoadingState(moduleKey, false);
          console.error(`❌ Failed to load module: ${moduleKey}`, error);
          subscriber.error(error);
        })
        .finally(() => {
          // Remove from loading map when done
          this.loadingModules.delete(moduleKey);
        });
    }).pipe(
      shareReplay(1) // Share the result with multiple subscribers
    );

    // Cache the loading observable
    this.loadingModules.set(moduleKey, loading$);
    
    return loading$;
  }

  /**
   * Check if a module is currently loading
   */
  isLoading(moduleKey: string): boolean {
    return this.loadingModules.has(moduleKey);
  }

  /**
   * Check if a module is already loaded
   */
  isLoaded(moduleKey: string): boolean {
    return this.loadedModules.has(moduleKey);
  }

  /**
   * Get loading state observable
   */
  getLoadingState(): Observable<{[key: string]: boolean}> {
    return this.loadingState.asObservable();
  }

  /**
   * Get loading state for a specific module
   */
  getModuleLoadingState(moduleKey: string): Observable<boolean> {
    return this.loadingState.pipe(
      map(state => state[moduleKey] || false)
    );
  }

  /**
   * Clear cache for a specific module (useful for development)
   */
  clearModuleCache(moduleKey: string): void {
    this.loadedModules.delete(moduleKey);
    this.loadingModules.delete(moduleKey);
    this.updateLoadingState(moduleKey, false);
    
    if (typeof window !== 'undefined' && (window as any)['ngDevMode']) {
      console.log(`🗑️ Module cache cleared: ${moduleKey}`);
    }
  }

  /**
   * Clear all module cache
   */
  clearAllCache(): void {
    this.loadedModules.clear();
    this.loadingModules.clear();
    this.loadingState.next({});
    
    if (typeof window !== 'undefined' && (window as any)['ngDevMode']) {
      console.log('🗑️ All module cache cleared');
    }
  }

  /**
   * Get statistics about loaded modules
   */
  getLoadingStats(): {
    loadedCount: number;
    loadingCount: number;
    loadedModules: string[];
    loadingModules: string[];
  } {
    return {
      loadedCount: this.loadedModules.size,
      loadingCount: this.loadingModules.size,
      loadedModules: Array.from(this.loadedModules.keys()),
      loadingModules: Array.from(this.loadingModules.keys())
    };
  }

  /**
   * Preload modules for better performance
   */
  preloadModules(modules: {key: string, loader: () => Promise<any>}[]): void {
    modules.forEach(module => {
      if (!this.isLoaded(module.key) && !this.isLoading(module.key)) {
        this.loadComponent(module.key, module.loader).subscribe({
          next: () => {
            if (typeof window !== 'undefined' && (window as any)['ngDevMode']) {
              console.log(`🚀 Preloaded module: ${module.key}`);
            }
          },
          error: (error) => {
            console.error(`❌ Failed to preload module: ${module.key}`, error);
          }
        });
      }
    });
  }

  private updateLoadingState(moduleKey: string, isLoading: boolean): void {
    const currentState = this.loadingState.value;
    this.loadingState.next({
      ...currentState,
      [moduleKey]: isLoading
    });
  }
}

/**
 * Helper function to create lazy loading routes with caching
 */
export function createLazyRoute(
  moduleKey: string,
  loader: () => Promise<any>,
  moduleLoaderService?: ModuleLoaderService
) {
  return () => {
    if (moduleLoaderService) {
      return moduleLoaderService.loadComponent(moduleKey, loader).toPromise();
    }
    return loader();
  };
}

/**
 * Decorator to track component loading
 */
export function TrackLoading(moduleKey: string) {
  return function(target: any) {
    const originalNgOnInit = target.prototype.ngOnInit;
    
    target.prototype.ngOnInit = function() {
      if (typeof window !== 'undefined' && (window as any)['ngDevMode']) {
        console.log(`🎯 Component initialized: ${moduleKey}`);
      }
      
      if (originalNgOnInit) {
        originalNgOnInit.call(this);
      }
    };
    
    return target;
  };
}
