# Single-stage build for simplicity and to avoid read-only filesystem issues
FROM node:20-alpine

# Install necessary tools
RUN apk add --no-cache curl

# Set working directory
WORKDIR /app

# Copy package files first (for better caching)
COPY package.json package-lock.json ./

# Install dependencies with optimized settings and legacy-peer-deps to handle Angular version conflicts
RUN npm config set fetch-timeout 600000 \
    && npm config set fetch-retries 5 \
    && npm ci --network-timeout 600000 --no-audit --prefer-offline --no-fund --legacy-peer-deps

# Copy source code
COPY . .

# Prepare fonts directory
RUN mkdir -p src/assets/fonts

# Download fonts (with fallback)
RUN curl -k -L "https://fonts.googleapis.com/css2?family=Inter:wght@100..900&family=Roboto:wght@100..900&display=swap" \
    -o src/assets/fonts/google-fonts.css || echo "Font download failed, continuing build"

# Modify angular.json to disable font inlining (combine commands to reduce layers)
RUN if [ -f "angular.json" ]; then \
      sed -i 's/"inlineStyleLanguage": "scss"/"inlineStyleLanguage": "scss", "optimization": {"fonts": false}/g' angular.json || \
      sed -i 's/"inlineStyleLanguage": "css"/"inlineStyleLanguage": "css", "optimization": {"fonts": false}/g' angular.json || \
      echo "Could not modify angular.json, continuing build"; \
    fi \
    && if [ -f "src/styles.css" ] && [ -f "src/assets/fonts/local-fonts.css" ]; then \
      echo "@import './assets/fonts/local-fonts.css';" | cat - src/styles.css > temp && mv temp src/styles.css; \
    fi

# Build the application with increased memory allocation and legacy-peer-deps
# Set API_URL for SSR build to avoid relative URL issues
ENV API_URL=http://localhost:8095/api/v1

# Modify angular.json to disable prerendering during build
RUN if [ -f "angular.json" ]; then \
      sed -i 's/"prerender": true/"prerender": false/g' angular.json || \
      echo "Could not modify prerender setting in angular.json, continuing build"; \
    fi

# Run the build with increased memory and legacy peer deps
RUN NODE_OPTIONS=--max_old_space_size=8192 npm --legacy-peer-deps run build:no-fonts

# Prune development dependencies to reduce image size
RUN npm prune --production --legacy-peer-deps \
    && npm cache clean --force

# Define build arguments with defaults
ARG NODE_ENV=production
ARG PORT=4001
ARG API_URL=http://localhost:8095/api/v1
ARG NODE_OPTIONS=--max_old_space_size=4096
ARG ENABLE_SSR=true
ARG CACHE_ENABLED=true

# Set environment variables from build args
ENV NODE_ENV=${NODE_ENV} \
    PORT=${PORT} \
    API_URL=${API_URL} \
    NODE_OPTIONS=${NODE_OPTIONS} \
    ENABLE_SSR=${ENABLE_SSR} \
    CACHE_ENABLED=${CACHE_ENABLED} \
    DOCKER_ENV=true

# Expose the port
EXPOSE 4001

# Copy entrypoint script
COPY docker-entrypoint.sh /usr/local/bin/
RUN chmod +x /usr/local/bin/docker-entrypoint.sh

# Use non-root user for better security
RUN addgroup -S appgroup && adduser -S appuser -G appgroup \
    && chown -R appuser:appgroup /app
USER appuser

# Set entrypoint and default command
ENTRYPOINT ["docker-entrypoint.sh"]
CMD ["node", "dist/smm-admin/server/server.mjs"]
