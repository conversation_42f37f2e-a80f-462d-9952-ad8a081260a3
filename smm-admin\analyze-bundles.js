#!/usr/bin/env node

/**
 * Bundle Analysis Script for SMM Admin
 * This script helps analyze and detect duplicate bundle loading issues
 */

const fs = require('fs');
const path = require('path');

function analyzeBundles() {
  console.log('🔍 Analyzing SMM Admin Bundle Configuration...\n');

  // Check for duplicate module imports
  checkDuplicateImports();
  
  // Check lazy loading configuration
  checkLazyLoading();
  
  // Check webpack configuration
  checkWebpackConfig();
  
  // Check Angular configuration
  checkAngularConfig();
  
  console.log('\n✅ Bundle analysis complete!');
}

function checkDuplicateImports() {
  console.log('📦 Checking for duplicate module imports...');
  
  const appConfigPath = path.join(__dirname, 'src/app/app.config.ts');
  if (fs.existsSync(appConfigPath)) {
    const content = fs.readFileSync(appConfigPath, 'utf8');
    
    // Check for TranslateModule duplicates
    const translateImports = content.match(/TranslateModule/g) || [];
    if (translateImports.length > 1) {
      console.log('⚠️  Warning: Multiple TranslateModule references found');
    } else {
      console.log('✅ TranslateModule: No duplicates found');
    }
    
    // Check for importProvidersFrom duplicates
    const importProviders = content.match(/importProvidersFrom/g) || [];
    console.log(`📊 importProvidersFrom usage: ${importProviders.length} times`);
  }
}

function checkLazyLoading() {
  console.log('\n🚀 Checking lazy loading configuration...');
  
  const routesPath = path.join(__dirname, 'src/app/app.routes.ts');
  if (fs.existsSync(routesPath)) {
    const content = fs.readFileSync(routesPath, 'utf8');
    
    // Count loadComponent usage
    const lazyComponents = content.match(/loadComponent:/g) || [];
    console.log(`✅ Lazy loaded components: ${lazyComponents.length}`);
    
    // Count direct component imports
    const directComponents = content.match(/component:/g) || [];
    const lazyCount = lazyComponents.length;
    const directCount = directComponents.length - lazyCount; // Subtract lazy ones
    
    console.log(`📊 Direct component imports: ${directCount}`);
    console.log(`📊 Lazy component imports: ${lazyCount}`);
    
    if (lazyCount > directCount) {
      console.log('✅ Good: More components are lazy loaded than directly imported');
    } else {
      console.log('⚠️  Consider converting more components to lazy loading');
    }
  }
}

function checkWebpackConfig() {
  console.log('\n⚙️  Checking webpack configuration...');
  
  const webpackPath = path.join(__dirname, 'webpack.config.js');
  if (fs.existsSync(webpackPath)) {
    const content = fs.readFileSync(webpackPath, 'utf8');
    
    if (content.includes('splitChunks')) {
      console.log('✅ Webpack bundle splitting configured');
    } else {
      console.log('⚠️  Webpack bundle splitting not configured');
    }
    
    if (content.includes('cacheGroups')) {
      console.log('✅ Webpack cache groups configured');
    } else {
      console.log('⚠️  Webpack cache groups not configured');
    }
  } else {
    console.log('⚠️  webpack.config.js not found');
  }
}

function checkAngularConfig() {
  console.log('\n🅰️  Checking Angular configuration...');
  
  const angularPath = path.join(__dirname, 'angular.json');
  if (fs.existsSync(angularPath)) {
    const content = fs.readFileSync(angularPath, 'utf8');
    const config = JSON.parse(content);
    
    const buildConfig = config.projects['smm-admin']?.architect?.build?.configurations;
    
    if (buildConfig?.development?.vendorChunk) {
      console.log('✅ Vendor chunk separation enabled');
    } else {
      console.log('⚠️  Vendor chunk separation not configured');
    }
    
    if (buildConfig?.development?.namedChunks) {
      console.log('✅ Named chunks enabled for debugging');
    } else {
      console.log('⚠️  Named chunks not enabled');
    }
    
    // Check budget configuration
    const budgets = buildConfig?.production?.budgets;
    if (budgets && budgets.length > 0) {
      console.log('✅ Bundle size budgets configured');
      budgets.forEach(budget => {
        console.log(`   - ${budget.type}: warning at ${budget.maximumWarning}, error at ${budget.maximumError}`);
      });
    } else {
      console.log('⚠️  Bundle size budgets not configured');
    }
  }
}

function generateRecommendations() {
  console.log('\n💡 Recommendations to prevent duplicate bundle loading:');
  console.log('1. ✅ Use lazy loading for large components');
  console.log('2. ✅ Configure webpack bundle splitting');
  console.log('3. ✅ Remove duplicate module imports');
  console.log('4. ✅ Use vendorChunk separation');
  console.log('5. ✅ Monitor bundle sizes with budgets');
  console.log('6. 🔄 Consider using Angular\'s built-in preloading strategies');
  console.log('7. 🔄 Implement tree shaking for unused code');
}

// Run analysis
if (require.main === module) {
  analyzeBundles();
  generateRecommendations();
}

module.exports = { analyzeBundles };
