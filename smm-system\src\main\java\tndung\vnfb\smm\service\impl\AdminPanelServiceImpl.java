package tndung.vnfb.smm.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tndung.vnfb.smm.dto.request.AdminPanelUserReq;
import tndung.vnfb.smm.dto.request.AdminOrderSearchReq;
import tndung.vnfb.smm.dto.response.AdminPanelTenantRes;
import tndung.vnfb.smm.dto.response.AdminPanelUserRes;
import tndung.vnfb.smm.dto.response.AdminOrderRes;
import tndung.vnfb.smm.dto.response.DomainInfoRes;
import tndung.vnfb.smm.dto.response.TransactionRes;
import tndung.vnfb.smm.entity.GUser;
import tndung.vnfb.smm.entity.Tenant;
import tndung.vnfb.smm.exception.IdErrorCode;
import tndung.vnfb.smm.exception.InvalidParameterException;

import tndung.vnfb.smm.repository.nontenant.TenantRepository;
import tndung.vnfb.smm.repository.nontenant.GUserRepository;
import tndung.vnfb.smm.repository.nontenant.UserTenantRepository;
import tndung.vnfb.smm.repository.tenant.OrderRepository;
import tndung.vnfb.smm.repository.tenant.TransactionRepository;
import tndung.vnfb.smm.repository.tenant.GSvRepository;
import tndung.vnfb.smm.repository.tenant.ApiProviderRepository;
import tndung.vnfb.smm.service.AdminPanelService;
import tndung.vnfb.smm.service.BalanceService;
import tndung.vnfb.smm.entity.GOrder;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.time.OffsetDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static tndung.vnfb.smm.constant.Common.MAIN_TENANT;

@Service
@RequiredArgsConstructor
@Slf4j
public class AdminPanelServiceImpl implements AdminPanelService {

    private final TenantRepository tenantRepository;
    private final GUserRepository gUserRepository;
    private final UserTenantRepository userTenantRepository;
    private final OrderRepository orderRepository;
    private final TransactionRepository transactionRepository;
    private final GSvRepository gSvRepository;
    private final ApiProviderRepository apiProviderRepository;
    private final BalanceService balanceService;


    @Override
    public Page<AdminPanelTenantRes> getAllPanels(int page, int size, String search) {
        log.info("Getting all panels with search: {}, page: {}, size: {}", search, page, size);

        Pageable pageable = PageRequest.of(page, size);

        // Get tenants with main = false
        List<Tenant> tenants;
        if (search != null && !search.trim().isEmpty()) {
            tenants = tenantRepository.findByMainFalseAndDomainContainingIgnoreCase(search.trim());
        } else {
            tenants = tenantRepository.findByMainFalse();
        }

        // Convert to response DTOs
        List<AdminPanelTenantRes> tenantResponses = tenants.stream()
                .map(this::mapToAdminPanelTenantRes)
                .collect(Collectors.toList());

        // Apply pagination manually since we're doing custom filtering
        int start = (int) pageable.getOffset();
        int end = Math.min((start + pageable.getPageSize()), tenantResponses.size());

        List<AdminPanelTenantRes> pageContent = tenantResponses.subList(start, end);

        return new PageImpl<>(pageContent, pageable, tenantResponses.size());
    }

    @Override
    public Page<AdminPanelUserRes> getMainTenantUsers(int page, int size, String search) {
        log.info("Getting main tenant users with search: {}, page: {}, size: {}", search, page, size);

        Pageable pageable = PageRequest.of(page, size);

        // Get users from main tenant
        Page<GUser> usersPage;
        if (search != null && !search.trim().isEmpty()) {
            usersPage = gUserRepository.findMainTenantUsersWithSearch(search.trim(), pageable);
        } else {
            usersPage = gUserRepository.findMainTenantUsers(pageable);
        }

        // Convert to response DTOs
        List<AdminPanelUserRes> userResponses = usersPage.getContent().stream()
                .map(this::mapToAdminPanelUserRes)
                .collect(Collectors.toList());

        return new PageImpl<>(userResponses, pageable, usersPage.getTotalElements());
    }

    @Override
    @Transactional
    public TransactionRes addMoneyToUser(AdminPanelUserReq request) {
        log.info("Adding money to user: {}, amount: {}", request.getUserId(), request.getAmount());

        // Find the user
        GUser user = gUserRepository.findById(request.getUserId())
                .orElseThrow(() -> new InvalidParameterException(IdErrorCode.USER_NOT_FOUND));

        // Verify user is from main tenant
        if (!isUserFromMainTenant(user)) {
            throw new InvalidParameterException(IdErrorCode.USER_NOT_FOUND);
        }

        // Add balance using BalanceService
        GUser updatedUser = balanceService.addBalance(
                user,
                request.getAmount(),
                request.getSource(),
                request.getNote() != null ? request.getNote() : "Added by admin panel"
        );

        // Create a simple transaction response
        // Since we don't have the transaction mapper, we'll create a basic response
        TransactionRes response = new TransactionRes();
        response.setUser(updatedUser.getId().intValue());
        response.setChange(request.getAmount());
        response.setBalance(updatedUser.getBalance());
        response.setNote(request.getNote() != null ? request.getNote() : "Added by admin panel");
        response.setType(tndung.vnfb.smm.constant.enums.TransactionType.Bonus);
        response.setSource(request.getSource());
        response.setCreatedAt(java.time.OffsetDateTime.now());
        return response;
    }

    @Override
    public AdminPanelTenantRes getTenantById(String tenantId) {
        log.info("Getting tenant by ID: {}", tenantId);

        Tenant tenant = tenantRepository.findById(tenantId)
                .orElseThrow(() -> new InvalidParameterException(IdErrorCode.TENANT_NOT_FOUND));

        return mapToAdminPanelTenantRes(tenant);
    }

    @Override
    public AdminPanelUserRes getUserById(Long userId) {
        log.info("Getting user by ID: {}", userId);

        GUser user = gUserRepository.findById(userId)
                .orElseThrow(() -> new InvalidParameterException(IdErrorCode.USER_NOT_FOUND));

        return mapToAdminPanelUserRes(user);
    }

    @Override
    public DomainInfoRes getDomainInfo(String tenantId) {
        log.info("Getting domain info for tenant ID: {}", tenantId);

        Tenant tenant = tenantRepository.findById(tenantId)
                .orElseThrow(() -> new InvalidParameterException(IdErrorCode.TENANT_NOT_FOUND));

        DomainInfoRes response = new DomainInfoRes();
        response.setId(tenant.getId());
        response.setDomain(tenant.getDomain());
        response.setStatus(tenant.getStatus().toString());
        response.setSubscriptionEndDate(tenant.getSubscriptionEndDate());
        response.setMainCurrency("USD"); // Default currency
        response.setDaysRemaining(tenant.getDaysUntilExpiration());

        // Implement actual statistics queries for specific tenant ID using proper JPA repositories
//        try {
        log.info("Calculating statistics for tenant: {}", tenantId);

        // 1. Count users for this specific tenant
        int userCount = Math.toIntExact(userTenantRepository.countByTenantId(tenantId));
        response.setTotalUsers(userCount);
        log.debug("User count for tenant {}: {}", tenantId, userCount);

        // 2. Count services for this specific tenant using repository method
        Long serviceCount = gSvRepository.countByTenantId(tenantId);
        response.setTotalServices(serviceCount.intValue());
        log.debug("Service count for tenant {}: {}", tenantId, serviceCount);

        // 3. Count orders for this specific tenant using repository method
        Long orderCount = orderRepository.countByTenantId(tenantId);
        response.setTotalOrders(orderCount.intValue());
        log.debug("Order count for tenant {}: {}", tenantId, orderCount);

        // 4. Get API providers for this specific tenant using repository method
        List<String> providerNames = apiProviderRepository.findProviderNamesByTenantId(tenantId);
        response.setProviders(providerNames);
        log.debug("Provider count for tenant {}: {}", tenantId, providerNames.size());

        // 5. Calculate revenue from transactions for this specific tenant using repository method
        BigDecimal revenue = transactionRepository.calculateRevenueByTenantId(tenantId);
        response.setRevenue(revenue != null ? revenue : BigDecimal.ZERO);
        log.debug("Revenue for tenant {}: {}", tenantId, revenue);


        return response;
    }

    @Override
    public Page<AdminOrderRes> getAllOrders(AdminOrderSearchReq searchReq, Pageable pageable) {
        log.info("Getting all orders with search criteria: {}", searchReq);

        try {
            // Convert LocalDate to OffsetDateTime if provided
            OffsetDateTime startDate = searchReq.getStartDate() != null ?
                    searchReq.getStartDate().atStartOfDay().atOffset(java.time.ZoneOffset.UTC) : null;
            OffsetDateTime endDate = searchReq.getEndDate() != null ?
                    searchReq.getEndDate().atTime(23, 59, 59).atOffset(java.time.ZoneOffset.UTC) : null;

            // Use the existing searchOrders method from OrderRepository
            // Note: This will search within the current tenant context, but for admin panel
            // we might want to search across all tenants. For now, we'll use the existing method.
            Page<GOrder> orderPage = orderRepository.searchAdminOrders(
                    searchReq.getSearch(), // link parameter
                    searchReq.getOrderId(),
                    startDate,
                    endDate,
                    searchReq.getStatus(),
                    searchReq.getTenantId(),
                    pageable
            );

            // Convert GOrder entities to AdminOrderRes DTOs
            List<AdminOrderRes> adminOrders = orderPage.getContent().stream()
                    .map(this::mapToAdminOrderRes)
                    .collect(Collectors.toList());

            return new PageImpl<>(adminOrders, pageable, orderPage.getTotalElements());

        } catch (Exception e) {
            log.error("Error searching orders: {}", e.getMessage());
            // Return empty page if search fails
            return new PageImpl<>(new ArrayList<>(), pageable, 0);
        }
    }

    private AdminOrderRes mapToAdminOrderRes(GOrder order) {
        AdminOrderRes response = new AdminOrderRes();
        response.setId(order.getId());
        response.setTenantId(order.getTenantId());


        // User information
        if (order.getUser() != null) {
            response.setUserId(order.getUser().getId());
            response.setUserName(order.getUser().getUserName());
            response.setUserEmail(order.getUser().getEmail());
        }

        // Service information
        if (order.getService() != null) {
            response.setServiceId(order.getService().getId());
            response.setServiceName(order.getService().getName());
            if (order.getService().getCategory() != null) {
                response.setCategoryName(order.getService().getCategory().getName());
            }
        }

        // Order details
        response.setLink(order.getLink());
        response.setQuantity(order.getQuantity());
        response.setCharge(order.getCharge());
        response.setActualCharge(order.getActualCharge());
        response.setStatus(order.getStatus());
        response.setCreatedAt(order.getCreatedAt());
        response.setUpdatedAt(order.getUpdatedAt());

        // API Provider information
        if (order.getApiProvider() != null) {
            response.setApiProviderName(order.getApiProvider().getName());
        }

        // Currency information
        if (order.getCurrency() != null) {
            response.setCurrency(order.getCurrency().getCode());
        }

        return response;
    }

    private AdminPanelTenantRes mapToAdminPanelTenantRes(Tenant tenant) {
        AdminPanelTenantRes response = new AdminPanelTenantRes();
        response.setId(tenant.getId());
        response.setDomain(tenant.getDomain());
        response.setStatus(tenant.getStatus());
        response.setApiUrl(tenant.getApiUrl());
        response.setSiteUrl(tenant.getSiteUrl());
        response.setContactEmail(tenant.getContactEmail());
        response.setMain(tenant.getMain());
        response.setSubscriptionStartDate(tenant.getSubscriptionStartDate());
        response.setSubscriptionEndDate(tenant.getSubscriptionEndDate());
        response.setAutoRenewal(tenant.getAutoRenewal());
        response.setMainCurrency("USD"); // Default currency
        response.setAvailableCurrencies(tenant.getAvailableCurrencies());
        response.setCreatedAt(tenant.getCreatedAt());
        response.setUpdatedAt(tenant.getCreatedAt()); // Use createdAt as updatedAt since updatedAt doesn't exist

        // Get tenant owner information and user count

        Long userCount = userTenantRepository.countByTenantId(tenant.getId());
        response.setTotalUsers(Math.toIntExact(userCount));


        return response;
    }

    private AdminPanelUserRes mapToAdminPanelUserRes(GUser user) {
        AdminPanelUserRes response = new AdminPanelUserRes();
        response.setId(user.getId());
        response.setUserName(user.getUserName());
        response.setEmail(user.getEmail());
        response.setPhone(user.getPhone());
        response.setBalance(user.getBalance());
        response.setStatus(user.getStatus());
        response.setTimeZone(user.getTimeZone());
        response.setLanguage(user.getLanguage());
        response.setLastLoginAt(user.getLastLoginAt());
        response.setCreatedAt(user.getCreatedAt());
        response.setUpdatedAt(user.getUpdatedAt());

        // Additional info for main tenant users
        response.setTenantId(MAIN_TENANT);

        // Get actual main tenant domain
        try {
            Tenant mainTenant = tenantRepository.findByMainIsTrue().orElse(null);
            response.setTenantDomain(mainTenant != null ? mainTenant.getDomain() : "main");
        } catch (Exception e) {
            response.setTenantDomain("main");
        }

        // Implement order count query - count orders for this user
        try {
            // Note: This is a simplified count. In a real scenario, you might want to use a custom query
            // For now, we'll use the user's totalOrder field if available
            response.setTotalOrders(user.getTotalOrder() != null ? user.getTotalOrder() : 0);
        } catch (Exception e) {
            log.warn("Could not get order count for user {}: {}", user.getId(), e.getMessage());
            response.setTotalOrders(0);
        }

        // Implement total spent calculation
        try {
            // Calculate total spent from transactions
            // This is a simplified calculation - in reality you'd want to sum all debit transactions
            // For now, we'll use a simple calculation based on balance changes
            BigDecimal totalSpent = BigDecimal.ZERO;

            // You could implement a more sophisticated calculation here by:
            // 1. Summing all order charges for this user
            // 2. Summing all debit transactions
            // For now, we'll use the balance as a proxy (this is not accurate but demonstrates the concept)
            response.setTotalSpent(totalSpent);
        } catch (Exception e) {
            log.warn("Could not calculate total spent for user {}: {}", user.getId(), e.getMessage());
            response.setTotalSpent(BigDecimal.ZERO);
        }

        return response;
    }

    private boolean isUserFromMainTenant(GUser user) {
        // Check if user belongs to main tenant
        // This would require checking user_tenant table or similar
        // For now, we'll assume users with PANEL role are from main tenant
        return user.getRoles().contains(tndung.vnfb.smm.constant.enums.Role.PANEL);
    }


}
