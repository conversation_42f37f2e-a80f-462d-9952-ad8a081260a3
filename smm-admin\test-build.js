#!/usr/bin/env node

/**
 * Simple test script to verify Angular build configuration
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 Testing SMM Admin Build Configuration...\n');

// Check if essential files exist
const requiredFiles = [
  'src/app/app.config.ts',
  'src/app/app.routes.ts',
  'angular.json',
  'package.json',
  'tsconfig.json'
];

let allFilesExist = true;

console.log('📁 Checking required files:');
requiredFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`✅ ${file}`);
  } else {
    console.log(`❌ ${file} - MISSING`);
    allFilesExist = false;
  }
});

if (!allFilesExist) {
  console.log('\n❌ Some required files are missing. Please check your project structure.');
  process.exit(1);
}

// Check app.config.ts for duplicate TranslateModule
console.log('\n🔧 Checking app.config.ts:');
const appConfigContent = fs.readFileSync('src/app/app.config.ts', 'utf8');

// Check for TranslateModule.forRoot
if (appConfigContent.includes('TranslateModule.forRoot')) {
  console.log('✅ TranslateModule.forRoot found');
} else {
  console.log('❌ TranslateModule.forRoot not found');
}

// Check for duplicate importProvidersFrom(TranslateModule)
if (appConfigContent.match(/importProvidersFrom\(\s*TranslateModule\s*\)/)) {
  console.log('⚠️  WARNING: Duplicate TranslateModule import found - this may cause bundle duplication');
} else {
  console.log('✅ No duplicate TranslateModule imports');
}

// Check routes for lazy loading
console.log('\n🚀 Checking routes configuration:');
const routesContent = fs.readFileSync('src/app/app.routes.ts', 'utf8');

const lazyLoadingCount = (routesContent.match(/loadComponent:/g) || []).length;
const directComponentCount = (routesContent.match(/component:/g) || []).length - lazyLoadingCount;

console.log(`📊 Lazy loaded components: ${lazyLoadingCount}`);
console.log(`📊 Direct component imports: ${directComponentCount}`);

if (lazyLoadingCount > directComponentCount) {
  console.log('✅ Good: More components use lazy loading');
} else {
  console.log('💡 Consider converting more components to lazy loading');
}

// Check Angular configuration
console.log('\n⚙️  Checking Angular configuration:');
const angularConfig = JSON.parse(fs.readFileSync('angular.json', 'utf8'));

const buildConfig = angularConfig.projects['smm-admin']?.architect?.build;
if (buildConfig?.builder === '@angular-devkit/build-angular:application') {
  console.log('✅ Using Angular 17+ application builder');
} else {
  console.log('⚠️  Not using Angular 17+ application builder');
}

const devConfig = buildConfig?.configurations?.development;
if (devConfig?.sourceMap) {
  console.log('✅ Source maps enabled for development');
} else {
  console.log('⚠️  Source maps not configured for development');
}

// Check budgets
const prodConfig = buildConfig?.configurations?.production;
if (prodConfig?.budgets && prodConfig.budgets.length > 0) {
  console.log('✅ Bundle size budgets configured');
  prodConfig.budgets.forEach(budget => {
    console.log(`   - ${budget.type}: warning at ${budget.maximumWarning}, error at ${budget.maximumError}`);
  });
} else {
  console.log('⚠️  Bundle size budgets not configured');
}

console.log('\n📋 Summary:');
console.log('✅ Configuration check completed');
console.log('\n💡 To test the application:');
console.log('1. Run: npm install');
console.log('2. Run: npm start');
console.log('3. Open browser and check Network tab for bundle loading');
console.log('4. Verify no duplicate bundle files are loaded');

console.log('\n🔍 To analyze bundles:');
console.log('1. Run: npm run build');
console.log('2. Check dist/smm-admin folder for generated bundles');
console.log('3. Use browser DevTools to analyze bundle sizes');

console.log('\n✅ Test completed successfully!');
