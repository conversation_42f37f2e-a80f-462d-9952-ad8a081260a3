# PowerShell script để test bundle optimization
Write-Host "🔍 Testing SMM Admin Bundle Optimization..." -ForegroundColor Green

# Function to check file exists
function Test-FileExists {
    param($Path, $Description)
    if (Test-Path $Path) {
        Write-Host "✅ $Description exists" -ForegroundColor Green
        return $true
    } else {
        Write-Host "❌ $Description missing" -ForegroundColor Red
        return $false
    }
}

# Function to check content in file
function Test-FileContent {
    param($Path, $Pattern, $Description)
    if (Test-Path $Path) {
        $content = Get-Content $Path -Raw
        if ($content -match $Pattern) {
            Write-Host "✅ $Description found" -ForegroundColor Green
            return $true
        } else {
            Write-Host "❌ $Description not found" -ForegroundColor Red
            return $false
        }
    } else {
        Write-Host "❌ File $Path not found" -ForegroundColor Red
        return $false
    }
}

Write-Host "`n📦 Checking file structure..." -ForegroundColor Yellow

# Check essential files
$files = @(
    @{Path="src/app/app.config.ts"; Desc="App configuration"},
    @{Path="src/app/app.routes.ts"; Desc="Routes configuration"},
    @{Path="webpack.config.js"; Desc="Webpack configuration"},
    @{Path="angular.json"; Desc="Angular configuration"},
    @{Path="analyze-bundles.js"; Desc="Bundle analysis script"},
    @{Path="src/app/core/services/module-loader.service.ts"; Desc="Module loader service"}
)

$allFilesExist = $true
foreach ($file in $files) {
    if (-not (Test-FileExists $file.Path $file.Desc)) {
        $allFilesExist = $false
    }
}

Write-Host "`n🔧 Checking configurations..." -ForegroundColor Yellow

# Check app.config.ts for duplicate TranslateModule
$appConfigChecks = @(
    @{Pattern="TranslateModule\.forRoot"; Desc="TranslateModule.forRoot configuration"},
    @{Pattern="importProvidersFrom\(\s*TranslateModule\s*\)"; Desc="Duplicate TranslateModule import (should NOT exist)"; ShouldExist=$false}
)

foreach ($check in $appConfigChecks) {
    $shouldExist = if ($check.ShouldExist -eq $false) { $false } else { $true }
    $result = Test-FileContent "src/app/app.config.ts" $check.Pattern $check.Desc
    
    if ($shouldExist -eq $false -and $result) {
        Write-Host "⚠️  Found duplicate TranslateModule import - this should be removed!" -ForegroundColor Yellow
    }
}

# Check routes for lazy loading
$routesChecks = @(
    @{Pattern="loadComponent:"; Desc="Lazy loading components"},
    @{Pattern="import\('\.\/components\/admin\/"; Desc="Admin components lazy loading"}
)

foreach ($check in $routesChecks) {
    Test-FileContent "src/app/app.routes.ts" $check.Pattern $check.Desc | Out-Null
}

# Check webpack configuration
$webpackChecks = @(
    @{Pattern="splitChunks"; Desc="Webpack bundle splitting"},
    @{Pattern="cacheGroups"; Desc="Webpack cache groups"},
    @{Pattern="runtimeChunk"; Desc="Runtime chunk separation"}
)

foreach ($check in $webpackChecks) {
    Test-FileContent "webpack.config.js" $check.Pattern $check.Desc | Out-Null
}

# Check Angular configuration
$angularChecks = @(
    @{Pattern='"vendorChunk":\s*true'; Desc="Vendor chunk separation"},
    @{Pattern='"namedChunks":\s*true'; Desc="Named chunks for debugging"}
)

foreach ($check in $angularChecks) {
    Test-FileContent "angular.json" $check.Pattern $check.Desc | Out-Null
}

Write-Host "`n📊 Checking package.json scripts..." -ForegroundColor Yellow

$packageJsonChecks = @(
    @{Pattern='"analyze-bundles"'; Desc="Bundle analysis script"},
    @{Pattern='"build:analyze"'; Desc="Build with analysis script"}
)

foreach ($check in $packageJsonChecks) {
    Test-FileContent "package.json" $check.Pattern $check.Desc | Out-Null
}

Write-Host "`n🧪 Running tests..." -ForegroundColor Yellow

# Test if Node.js script runs
try {
    Write-Host "Testing bundle analysis script..." -ForegroundColor Cyan
    $result = node analyze-bundles.js 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Bundle analysis script runs successfully" -ForegroundColor Green
    } else {
        Write-Host "❌ Bundle analysis script failed" -ForegroundColor Red
        Write-Host $result -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Error running bundle analysis script: $_" -ForegroundColor Red
}

# Test Angular build (dry run)
Write-Host "`n🏗️  Testing Angular build configuration..." -ForegroundColor Yellow
try {
    Write-Host "Checking Angular CLI..." -ForegroundColor Cyan
    $ngVersion = ng version 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Angular CLI is available" -ForegroundColor Green
    } else {
        Write-Host "❌ Angular CLI not found" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Error checking Angular CLI: $_" -ForegroundColor Red
}

Write-Host "`n📋 Summary and Recommendations:" -ForegroundColor Yellow

if ($allFilesExist) {
    Write-Host "✅ All required files are present" -ForegroundColor Green
} else {
    Write-Host "❌ Some required files are missing" -ForegroundColor Red
}

Write-Host "`n🚀 Next steps:" -ForegroundColor Cyan
Write-Host "1. Run 'npm install' to ensure all dependencies are installed" -ForegroundColor White
Write-Host "2. Run 'npm run analyze-bundles' to analyze current bundle state" -ForegroundColor White
Write-Host "3. Run 'npm start' to test the application" -ForegroundColor White
Write-Host "4. Open browser DevTools Network tab to verify bundle loading" -ForegroundColor White
Write-Host "5. Run 'npm run build:analyze' to analyze production bundles" -ForegroundColor White

Write-Host "`n💡 Performance tips:" -ForegroundColor Cyan
Write-Host "- Monitor bundle sizes in Network tab" -ForegroundColor White
Write-Host "- Check for duplicate file loads" -ForegroundColor White
Write-Host "- Verify lazy loading is working" -ForegroundColor White
Write-Host "- Use Lighthouse for performance audits" -ForegroundColor White

Write-Host "`n✅ Bundle optimization test completed!" -ForegroundColor Green
