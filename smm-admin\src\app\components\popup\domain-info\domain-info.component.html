<!-- Domain Info Modal -->
<div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" (click)="onClose()">
  <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto" (click)="$event.stopPropagation()">
    <!-- Header -->
    <div class="flex items-center justify-between p-6 border-b border-gray-200">
      <h2 class="text-xl font-semibold text-gray-900">
        {{ 'admin.domain_info.title' | translate }}
      </h2>
      <button 
        (click)="onClose()"
        class="text-gray-400 hover:text-gray-600 transition-colors">
        <i class="fas fa-times text-xl"></i>
      </button>
    </div>

    <!-- Content -->
    <div class="p-6">
      <!-- Loading State -->
      <div *ngIf="isLoading" class="flex items-center justify-center py-8">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span class="ml-3 text-gray-600">{{ 'admin.order_tracking.loading' | translate }}</span>
      </div>

      <!-- Error State -->
      <div *ngIf="error && !isLoading" class="text-center py-8">
        <div class="text-red-600 mb-4">
          <i class="fas fa-exclamation-triangle text-3xl"></i>
        </div>
        <p class="text-gray-600">{{ error }}</p>
        <button 
          (click)="loadDomainInfo()"
          class="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
          Try Again
        </button>
      </div>

      <!-- Domain Info Content -->
      <div *ngIf="domainInfo && !isLoading && !error">
        <!-- Domain Header -->
        <div class="mb-6 p-4 bg-gray-50 rounded-lg">
          <h3 class="text-lg font-medium text-gray-900 mb-2">{{ domainInfo.domain }}</h3>
          <div class="flex items-center space-x-4">
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                  [class]="domainInfo.status === 'ACTIVE' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'">
              {{ domainInfo.status }}
            </span>
            <span class="text-sm text-gray-600">ID: {{ domainInfo.id }}</span>
          </div>
        </div>

        <!-- Statistics Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <!-- Subscription Info -->
          <div class="bg-blue-50 p-4 rounded-lg">
            <h4 class="font-medium text-gray-900 mb-3">{{ 'admin.domain_info.expiry_date' | translate }}</h4>
            <div class="space-y-2">
              <p class="text-sm text-gray-600">
                <span class="font-medium">{{ 'admin.domain_info.expiry_date' | translate }}:</span>
                {{ formatDate(domainInfo.subscription_end_date) }}
              </p>
              <p class="text-sm" [class]="getDaysRemainingClass(domainInfo.days_remaining)">
                <span class="font-medium">{{ 'admin.domain_info.days_remaining' | translate }}:</span>
                {{ domainInfo.days_remaining }} 
                <span class="ml-1">{{ getDaysRemainingText(domainInfo.days_remaining) | translate }}</span>
              </p>
            </div>
          </div>

          <!-- Revenue -->
          <div class="bg-green-50 p-4 rounded-lg">
            <h4 class="font-medium text-gray-900 mb-3">{{ 'admin.domain_info.revenue' | translate }}</h4>
            <p class="text-2xl font-bold text-green-600">
              {{ formatCurrency(domainInfo.revenue, domainInfo.main_currency) }}
            </p>
          </div>

          <!-- Users -->
          <div class="bg-purple-50 p-4 rounded-lg">
            <h4 class="font-medium text-gray-900 mb-3">{{ 'admin.domain_info.total_users' | translate }}</h4>
            <p class="text-2xl font-bold text-purple-600">{{ domainInfo.total_users }}</p>
          </div>

          <!-- Services -->
          <div class="bg-orange-50 p-4 rounded-lg">
            <h4 class="font-medium text-gray-900 mb-3">{{ 'admin.domain_info.total_services' | translate }}</h4>
            <p class="text-2xl font-bold text-orange-600">{{ domainInfo.total_services }}</p>
          </div>

          <!-- Orders -->
          <div class="bg-indigo-50 p-4 rounded-lg">
            <h4 class="font-medium text-gray-900 mb-3">{{ 'admin.domain_info.total_orders' | translate }}</h4>
            <p class="text-2xl font-bold text-indigo-600">{{ domainInfo.total_orders }}</p>
          </div>

          <!-- Providers -->
          <div class="bg-gray-50 p-4 rounded-lg">
            <h4 class="font-medium text-gray-900 mb-3">{{ 'admin.domain_info.providers' | translate }}</h4>
            <div class="space-y-1">
              <div *ngIf="domainInfo.providers.length === 0" class="text-sm text-gray-500">
                No providers
              </div>
              <div *ngFor="let provider of domainInfo.providers" class="text-sm text-gray-700">
                • {{ provider }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Footer -->
    <div class="flex justify-end p-6 border-t border-gray-200">
      <button 
        (click)="onClose()"
        class="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">
        {{ 'admin.domain_info.close' | translate }}
      </button>
    </div>
  </div>
</div>
