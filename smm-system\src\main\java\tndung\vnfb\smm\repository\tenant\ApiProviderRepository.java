package tndung.vnfb.smm.repository.tenant;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import tndung.vnfb.smm.entity.ApiProvider;

import java.util.List;
import java.util.Optional;

public interface ApiProviderRepository extends TenantAwareRepository<ApiProvider, Long> {

    @Query("SELECT a FROM ApiProvider a WHERE a.url = :url AND (a.tenantId = :#{T(tndung.vnfb.smm.config.TenantContext).getWildcardTenant()} or :#{T(tndung.vnfb.smm.config.TenantContext).getWildcardTenant()} = '*')" )
    List<ApiProvider> findByUrl(@Param("url") String url);


    @Query("SELECT a FROM ApiProvider a WHERE a.url = :url AND a.isDeleted = false AND (a.tenantId = :#{T(tndung.vnfb.smm.config.TenantContext).getWildcardTenant()} or :#{T(tndung.vnfb.smm.config.TenantContext).getWildcardTenant()} = '*')" )
    Optional<ApiProvider> findByUrlWithDeletedFalse(@Param("url") String url);
    @Query("SELECT a FROM ApiProvider a WHERE a.id = :id and a.isDeleted = false AND ( a.tenantId = :#{T(tndung.vnfb.smm.config.TenantContext).getWildcardTenant()} or :#{T(tndung.vnfb.smm.config.TenantContext).getWildcardTenant()} = '*')" )
    Optional<ApiProvider> findByIsDeletedFalse(@Param("id")  Long id);
    @Query("SELECT p from ApiProvider  p WHERE p.isDeleted = false and (p.tenantId = :#{T(tndung.vnfb.smm.config.TenantContext).getCurrentTenant()} or :#{T(tndung.vnfb.smm.config.TenantContext).getWildcardTenant()} = '*')")
    List<ApiProvider> getAll();
    /**
     * Find API provider names for a specific tenant ID
     */
    @Query("SELECT a.name FROM ApiProvider a WHERE a.tenantId = :tenantId AND a.isDeleted = false")
    List<String> findProviderNamesByTenantId(@Param("tenantId") String tenantId);
}
