/**
 * ESBuild configuration for Angular 17 SMM Admin
 * This configuration helps optimize bundle loading and prevent duplicates
 */

const { build } = require('esbuild');

module.exports = {
  // Define global variables for compatibility
  define: {
    global: 'globalThis',
  },
  
  // Bundle splitting configuration
  splitting: true,
  
  // Code splitting options
  chunkNames: '[name]-[hash]',
  
  // External dependencies that should not be bundled
  external: [
    // Keep these external to prevent duplicate loading
  ],
  
  // Plugin to handle module resolution
  plugins: [
    {
      name: 'module-resolver',
      setup(build) {
        // Handle global variable for sockjs-client
        build.onResolve({ filter: /^sockjs-client$/ }, args => {
          return {
            path: args.path,
            namespace: 'sockjs-client',
          };
        });
        
        // Log bundle information in development
        build.onEnd(result => {
          if (process.env.NODE_ENV === 'development') {
            console.log('📦 Bundle build completed');
            if (result.errors.length > 0) {
              console.error('❌ Build errors:', result.errors);
            }
            if (result.warnings.length > 0) {
              console.warn('⚠️ Build warnings:', result.warnings);
            }
          }
        });
      },
    },
  ],
  
  // Optimization settings
  minify: false, // Disable in development
  sourcemap: true,
  
  // Tree shaking
  treeShaking: true,
  
  // Target modern browsers
  target: ['es2020', 'chrome80', 'firefox78', 'safari14'],
  
  // Format
  format: 'esm',
  
  // Platform
  platform: 'browser',
};

/**
 * Custom build function for development
 */
async function buildDevelopment() {
  try {
    console.log('🚀 Starting development build...');
    
    const result = await build({
      ...module.exports,
      entryPoints: ['src/main.ts'],
      outdir: 'dist/smm-admin',
      watch: true,
    });
    
    console.log('✅ Development build completed successfully');
    return result;
  } catch (error) {
    console.error('❌ Build failed:', error);
    process.exit(1);
  }
}

/**
 * Custom build function for production
 */
async function buildProduction() {
  try {
    console.log('🏗️ Starting production build...');
    
    const result = await build({
      ...module.exports,
      entryPoints: ['src/main.ts'],
      outdir: 'dist/smm-admin',
      minify: true,
      sourcemap: false,
      metafile: true, // Generate bundle analysis
    });
    
    console.log('✅ Production build completed successfully');
    
    // Log bundle analysis
    if (result.metafile) {
      console.log('📊 Bundle analysis available in metafile');
    }
    
    return result;
  } catch (error) {
    console.error('❌ Production build failed:', error);
    process.exit(1);
  }
}

// Export build functions
module.exports.buildDevelopment = buildDevelopment;
module.exports.buildProduction = buildProduction;

// Run build if called directly
if (require.main === module) {
  const isDevelopment = process.env.NODE_ENV === 'development';
  
  if (isDevelopment) {
    buildDevelopment();
  } else {
    buildProduction();
  }
}
