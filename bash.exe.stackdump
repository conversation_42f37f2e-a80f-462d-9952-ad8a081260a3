Stack trace:
Frame         Function      Args
0007FFFFBBB0  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFFBBB0, 0007FFFFAAB0) msys-2.0.dll+0x1FE8E
0007FFFFBBB0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFBE88) msys-2.0.dll+0x67F9
0007FFFFBBB0  000210046832 (000210286019, 0007FFFFBA68, 0007FFFFBBB0, 000000000000) msys-2.0.dll+0x6832
0007FFFFBBB0  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFBBB0  000210068E24 (0007FFFFBBC0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFBE90  00021006A225 (0007FFFFBBC0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFB112B0000 ntdll.dll
7FFB105A0000 KERNEL32.DLL
7FFB0ECD0000 KERNELBASE.dll
7FFB10670000 USER32.dll
7FFB0F1A0000 win32u.dll
000210040000 msys-2.0.dll
7FFB109F0000 GDI32.dll
7FFB0E9F0000 gdi32full.dll
7FFB0E950000 msvcp_win.dll
7FFB0F1D0000 ucrtbase.dll
7FFB0FF70000 advapi32.dll
7FFB10350000 msvcrt.dll
7FFB0F690000 sechost.dll
7FFB10A50000 RPCRT4.dll
7FFB0EB10000 bcrypt.dll
7FFB0E1A0000 CRYPTBASE.DLL
7FFB0EC40000 bcryptPrimitives.dll
7FFB10A20000 IMM32.DLL
