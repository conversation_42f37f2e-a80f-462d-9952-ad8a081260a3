export interface AdminOrder {
  id: number;
  tenant_id: string;
  tenant_domain: string;
  user_id: number;
  user_name: string;
  user_email: string;
  service_id: number;
  service_name: string;
  category_name: string;
  link: string;
  quantity: number;
  charge: number;
  actual_charge: number;
  status: string;
  created_at: string;
  updated_at: string;
  api_provider_name: string;
  currency: string;
}

export interface AdminOrderSearchRequest {
  search?: string;
  tenantId?: string;
  status?: string;
  startDate?: string;
  endDate?: string;
  pageNumber?: number;
  pageSize?: number;
}
