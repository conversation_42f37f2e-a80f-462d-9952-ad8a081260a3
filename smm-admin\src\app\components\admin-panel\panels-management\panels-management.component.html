<!-- Search and Filters -->
<div class="mb-4 flex flex-col sm:flex-row gap-4">
  <div class="flex-1">
    <div class="relative">
      <input
        type="text"
        [(ngModel)]="searchTerm"
        (keyup.enter)="search()"
        placeholder="Search by domain..."
        class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
      <fa-icon [icon]="['fas', 'search']" class="absolute left-3 top-3 text-gray-400"></fa-icon>
    </div>
  </div>
  <div class="flex gap-2">
    <button
      (click)="search()"
      class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
      <fa-icon [icon]="['fas', 'search']" class="mr-2"></fa-icon>
      Search
    </button>
    <button
      (click)="resetSearch()"
      class="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">
      <fa-icon [icon]="['fas', 'times']" class="mr-2"></fa-icon>
      Reset
    </button>
  </div>
</div>

<!-- Panels Table -->
<div class="bg-white rounded-lg shadow overflow-hidden">
  <div class="overflow-x-auto">
    <table class="min-w-full divide-y divide-gray-200">
      <thead class="bg-gray-50">
        <tr>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Domain</th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contact Email</th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Subscription</th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Users</th>
        </tr>
      </thead>
      <tbody class="bg-white divide-y divide-gray-200">
        <tr *ngFor="let panel of panels" class="hover:bg-gray-50">
          <td class="px-6 py-4 whitespace-nowrap">
            <div class="text-sm  font-medium text-blue-500 cursor-pointer hover:text-blue-600 transition-colors"
                 (click)="onOpenDomainInfo(panel.id, panel.domain)">
              {{ panel.domain }}
            </div>
            <div class="text-sm text-gray-500">{{ panel.id }}</div>
          </td>
          <td class="px-6 py-4 whitespace-nowrap">
            <span [class]="getStatusClass(panel.status)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
              {{ panel.status }}
            </span>
          </td>
          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
            {{ panel.contact_email || '-' }}
          </td>
          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
            <div *ngIf="panel.subscription_end_date">
              Expires: {{ formatDate(panel.subscription_end_date) }}
            </div>
            <div *ngIf="!panel.subscription_end_date" class="text-gray-500">-</div>
          </td>
          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
            {{ formatDate(panel.created_at) }}
          </td>
          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
            {{ panel.total_users || 0 }}
          </td>
        </tr>
        <tr *ngIf="panels.length === 0 && !isLoading">
          <td colspan="6" class="px-6 py-4 text-center text-gray-500">
            No panels found
          </td>
        </tr>
      </tbody>
    </table>
  </div>

  <!-- Pagination -->
  <div *ngIf="pagination.total_pages > 1" class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
    <div class="flex-1 flex justify-between sm:hidden">
      <button
        (click)="loadPage(pagination.page_number - 1)"
        [disabled]="pagination.page_number === 0"
        class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50">
        Previous
      </button>
      <button
        (click)="loadPage(pagination.page_number + 1)"
        [disabled]="pagination.page_number >= pagination.total_pages - 1"
        class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50">
        Next
      </button>
    </div>
    <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
      <div>
        <p class="text-sm text-gray-700">
          Showing {{ pagination.page_number * pagination.page_size + 1 }} to 
          {{ Math.min((pagination.page_number + 1) * pagination.page_size, pagination.total_elements) }} of 
          {{ pagination.total_elements }} results
        </p>
      </div>
      <div>
        <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
          <button
            (click)="loadPage(pagination.page_number - 1)"
            [disabled]="pagination.page_number === 0"
            class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50">
            <fa-icon [icon]="['fas', 'chevron-left']"></fa-icon>
          </button>
          <button
            (click)="loadPage(pagination.page_number + 1)"
            [disabled]="pagination.page_number >= pagination.total_pages - 1"
            class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50">
            <fa-icon [icon]="['fas', 'chevron-right']"></fa-icon>
          </button>
        </nav>
      </div>
    </div>
  </div>
</div>
