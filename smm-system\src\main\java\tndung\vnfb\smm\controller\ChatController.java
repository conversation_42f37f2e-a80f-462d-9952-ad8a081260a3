package tndung.vnfb.smm.controller;

import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.handler.annotation.SendTo;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import tndung.vnfb.smm.anotation.TenantCheck;
import tndung.vnfb.smm.config.TenantContext;
import tndung.vnfb.smm.dto.ApiResponseEntity;
import tndung.vnfb.smm.dto.chat.*;
import tndung.vnfb.smm.dto.response.FileUploadResponse;
import tndung.vnfb.smm.service.ChatService;
import tndung.vnfb.smm.service.FileUploadService;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/v1/chat")
@RequiredArgsConstructor
public class ChatController {

    private final ChatService chatService;
    private final FileUploadService fileUploadService;

    /**
     * Create a new chat room
     */
    @PostMapping("/rooms")
    @PreAuthorize("hasAnyRole('ROLE_PANEL', 'ROLE_ADMIN_PANEL')")

    public ApiResponseEntity<ChatRoomRes> createChatRoom(@Valid @RequestBody ChatRoomReq req) {
        return ApiResponseEntity.success(chatService.createChatRoom(req));
    }

    /**
     * Get chat rooms for current user
     */
    @GetMapping("/rooms")
    @PreAuthorize("hasAnyRole('ROLE_PANEL', 'ROLE_ADMIN_PANEL')")

    public ApiResponseEntity<Page<ChatRoomRes>> getChatRooms(Pageable pageable) {
        return ApiResponseEntity.success(chatService.getChatRooms(pageable));
    }

    /**
     * Get or create direct chat with another user
     */
    @PostMapping("/rooms/direct/{userId}")
    @PreAuthorize("hasAnyRole('ROLE_PANEL', 'ROLE_ADMIN_PANEL')")

    public ApiResponseEntity<ChatRoomRes> getOrCreateDirectChat(@PathVariable Long userId) {
        return ApiResponseEntity.success(chatService.getOrCreateDirectChat(userId));
    }

    /**
     * Get chat room details
     */
    @GetMapping("/rooms/{roomId}")
    @PreAuthorize("hasAnyRole('ROLE_PANEL', 'ROLE_ADMIN_PANEL')")

    public ApiResponseEntity<ChatRoomRes> getChatRoom(@PathVariable Long roomId) {
        return ApiResponseEntity.success(chatService.getChatRoom(roomId));
    }

    /**
     * Delete a chat room
     */
    @DeleteMapping("/rooms/{roomId}")
    @PreAuthorize("hasAnyRole('ROLE_PANEL', 'ROLE_ADMIN_PANEL')")

    public ResponseEntity<Void> deleteChatRoom(@PathVariable Long roomId) {
        chatService.deleteChatRoom(roomId);
        return ResponseEntity.ok().build();
    }

    /**
     * Send a message to a chat room
     */
    @PostMapping("/messages")
    @PreAuthorize("hasAnyRole('ROLE_PANEL', 'ROLE_ADMIN_PANEL')")

    public ApiResponseEntity<ChatMessageRes> sendMessageToRoom(@Valid @RequestBody ChatMessageReq req) {
        return ApiResponseEntity.success(chatService.sendMessage(req));
    }

    /**
     * Get messages in a chat room
     */
    @GetMapping("/rooms/{roomId}/messages")
    @PreAuthorize("hasAnyRole('ROLE_PANEL', 'ROLE_ADMIN_PANEL')")
    public ApiResponseEntity<Page<ChatMessageRes>> getMessages(@PathVariable Long roomId, Pageable pageable) {
        return ApiResponseEntity.success(chatService.getMessages(roomId, pageable));
    }

    /**
     * Mark messages as read in a chat room
     */
    @PostMapping("/rooms/{roomId}/read")
    @PreAuthorize("hasAnyRole('ROLE_PANEL', 'ROLE_ADMIN_PANEL')")

    public ResponseEntity<Void> markAsRead(@PathVariable Long roomId) {
        chatService.markAsRead(roomId);
        return ResponseEntity.ok().build();
    }

    /**
     * Get users available for chat
     */
    @GetMapping("/users")
    @PreAuthorize("hasAnyRole('ROLE_PANEL', 'ROLE_ADMIN_PANEL')")
    public ApiResponseEntity<List<ChatUserRes>> getAvailableUsers() {
        return ApiResponseEntity.success(chatService.getAvailableUsers());
    }



    /**
     * WebSocket message mapping for real-time chat
     */
    @MessageMapping("/chat.sendMessage")
    @SendTo("/topic/public")
    public ChatMessageRes sendMessage(ChatMessageReq chatMessage) {
        return chatService.sendMessage(chatMessage);
    }

    /**
     * Support Chat APIs for PANEL users to send messages to ADMIN_PANEL users
     */
    @PostMapping("/support/messages")
    @PreAuthorize("hasRole('ROLE_PANEL')")
    public ApiResponseEntity<ChatMessageRes> sendSupportMessage(@RequestBody ChatMessageReq req) {
        return ApiResponseEntity.success(chatService.sendSupportMessage(req));
    }

    @GetMapping("/support/messages")
    @PreAuthorize("hasAnyRole('ROLE_PANEL', 'ROLE_ADMIN_PANEL')")
    public ApiResponseEntity<Page<ChatMessageRes>> getSupportMessages(Pageable pageable) {
        return ApiResponseEntity.success(chatService.getSupportMessages(pageable));
    }

    @GetMapping("/support/rooms")
    @PreAuthorize("hasRole('ROLE_ADMIN_PANEL')")
    public ApiResponseEntity<Page<ChatRoomRes>> getSupportChatRooms(Pageable pageable) {
        return ApiResponseEntity.success(chatService.getSupportChatRooms(pageable));
    }

    @GetMapping("/support/users/{userId}/messages")
    @PreAuthorize("hasRole('ROLE_ADMIN_PANEL')")
    public ApiResponseEntity<Page<ChatMessageRes>> getSupportChatMessages(@PathVariable Long userId, Pageable pageable) {
        return ApiResponseEntity.success(chatService.getSupportChatMessages(userId, pageable));
    }

    @PostMapping("/support/users/{userId}/reply")
    @PreAuthorize("hasRole('ROLE_ADMIN_PANEL')")
    public ApiResponseEntity<ChatMessageRes> sendSupportReply(@PathVariable Long userId, @RequestBody ChatMessageReq req) {
        return ApiResponseEntity.success(chatService.sendSupportReply(userId, req));
    }

    @GetMapping("/support/room")
    @PreAuthorize("hasRole('ROLE_PANEL')")
    public ApiResponseEntity<ChatRoomRes> getOrCreateSupportChatRoom() {
        return ApiResponseEntity.success(chatService.getOrCreateSupportChatRoom());
    }

    @PostMapping("/support/admin/{adminUserId}/add-to-rooms")
    @PreAuthorize("hasRole('ROLE_ADMIN_PANEL')")
    public ApiResponseEntity<Void> addAdminToAllSupportChatRooms(@PathVariable Long adminUserId) {
        chatService.addAdminToAllSupportChatRooms(adminUserId);
        return ApiResponseEntity.success(null);
    }

    /**
     * Upload file for chat (images only)
     * @param file The image file to upload
     * @return The uploaded file information
     */
    @PostMapping(value = "/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @PreAuthorize("hasAnyRole('ROLE_PANEL', 'ROLE_ADMIN_PANEL')")
    @TenantCheck
    public ApiResponseEntity<FileUploadResponse> uploadChatFile(
            @RequestParam("file") MultipartFile file) {
        String tenantId = TenantContext.getWildcardTenant();
        return ApiResponseEntity.success(fileUploadService.uploadChatFile(file, tenantId));
    }
}
