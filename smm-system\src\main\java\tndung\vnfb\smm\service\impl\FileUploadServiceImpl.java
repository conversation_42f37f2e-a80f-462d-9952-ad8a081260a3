package tndung.vnfb.smm.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;
import tndung.vnfb.smm.dto.design.FaviconDto;
import tndung.vnfb.smm.dto.design.LogoDto;
import tndung.vnfb.smm.dto.response.FileUploadResponse;
import tndung.vnfb.smm.entity.Tenant;
import tndung.vnfb.smm.exception.FileStorageException;
import tndung.vnfb.smm.service.DesignSettingsService;
import tndung.vnfb.smm.service.FileUploadService;
import tndung.vnfb.smm.service.TenantService;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;

/**
 * Implementation of the FileUploadService
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class FileUploadServiceImpl implements FileUploadService {
    private final DesignSettingsService designSettingsService;
    private final TenantService tenantService;

    @Value("${file.upload.dir:/etc/nginx/cdn}")
    private String uploadDir;

    @Value("${file.cdn.url:http://localhost/cdn}")
    private String cdnUrl;

    /**
     * Upload a logo file
     * @param file The logo file to upload
     * @param tenantId The tenant ID
     * @return The uploaded file information
     */
    @Override
    public FileUploadResponse uploadLogo(MultipartFile file, String tenantId) {
        try {
            // First, ensure the base upload directory exists with proper permissions
            Path baseUploadPath = Paths.get(uploadDir).toAbsolutePath().normalize();
            if (!Files.exists(baseUploadPath)) {
                try {
                    Files.createDirectories(baseUploadPath);
                    // Set permissions to 777 (rwxrwxrwx) to ensure write access
                    baseUploadPath.toFile().setReadable(true, false);
                    baseUploadPath.toFile().setWritable(true, false);
                    baseUploadPath.toFile().setExecutable(true, false);
                    log.info("Created base upload directory: {}", baseUploadPath);
                } catch (IOException e) {
                    log.error("Failed to create base upload directory: {}", baseUploadPath, e);
                    throw new FileStorageException("Could not create upload directory. Please check permissions!", e);
                }
            }

            // Create the tenant-specific directory
            String tenantDir = uploadDir + "/" + tenantId;
            Path uploadPath = Paths.get(tenantDir).toAbsolutePath().normalize();
            if (!Files.exists(uploadPath)) {
                Files.createDirectories(uploadPath);
                // Set permissions to 777 (rwxrwxrwx) to ensure write access
                uploadPath.toFile().setReadable(true, false);
                uploadPath.toFile().setWritable(true, false);
                uploadPath.toFile().setExecutable(true, false);
                log.info("Created tenant upload directory: {}", uploadPath);
            }

            // Generate a unique file name
            String originalFileName = StringUtils.cleanPath(Objects.requireNonNull(file.getOriginalFilename()));
            String fileExtension = getFileExtension(originalFileName);
            String fileName = "logo_" + UUID.randomUUID() + "." + fileExtension;

            // Save the file
            Path targetLocation = uploadPath.resolve(fileName);
            Files.copy(file.getInputStream(), targetLocation, StandardCopyOption.REPLACE_EXISTING);

            // Get tenant domain for the CDN URL
            Optional<Tenant> tenantOpt = tenantService.findByTenantId(tenantId);
            String domain = tenantOpt.map(Tenant::getDomain).orElse(null);

            // Create the file URL with tenant domain if available
            String fileUrl;
            if (domain != null && !domain.isEmpty()) {
                // Use https protocol for the CDN URL with tenant domain
                fileUrl = "https://" + domain + "/cdn/" + tenantId + "/" + fileName;
                log.info("Using tenant domain for CDN URL: {}", fileUrl);
            } else {
                // Fallback to the default CDN URL
                fileUrl = cdnUrl + "/" + tenantId + "/" + fileName;
                log.info("Using default CDN URL: {}", fileUrl);
            }

            // Update the design settings
            LogoDto logoDto = new LogoDto();
            logoDto.setUrl(fileUrl);
            logoDto.setFileType(fileExtension);
            designSettingsService.updateLogo(tenantId, logoDto);

            // Return the response
            return FileUploadResponse.builder()
                    .fileName(fileName)
                    .fileType(file.getContentType())
                    .url(fileUrl)
                    .size(file.getSize())
                    .build();
        } catch (IOException ex) {
            log.error("Error uploading logo file", ex);
            throw new FileStorageException("Could not store file. Please try again!", ex);
        }
    }

    /**
     * Upload a favicon file
     * @param file The favicon file to upload
     * @param tenantId The tenant ID
     * @return The uploaded file information
     */
    @Override
    public FileUploadResponse uploadFavicon(MultipartFile file, String tenantId) {
        try {
            // First, ensure the base upload directory exists with proper permissions
            Path baseUploadPath = Paths.get(uploadDir).toAbsolutePath().normalize();
            if (!Files.exists(baseUploadPath)) {
                try {
                    Files.createDirectories(baseUploadPath);
                    // Set permissions to 777 (rwxrwxrwx) to ensure write access
                    baseUploadPath.toFile().setReadable(true, false);
                    baseUploadPath.toFile().setWritable(true, false);
                    baseUploadPath.toFile().setExecutable(true, false);
                    log.info("Created base upload directory: {}", baseUploadPath);
                } catch (IOException e) {
                    log.error("Failed to create base upload directory: {}", baseUploadPath, e);
                    throw new FileStorageException("Could not create upload directory. Please check permissions!", e);
                }
            }

            // Create the tenant-specific directory
            String tenantDir = uploadDir + "/" + tenantId;
            Path uploadPath = Paths.get(tenantDir).toAbsolutePath().normalize();
            if (!Files.exists(uploadPath)) {
                Files.createDirectories(uploadPath);
                // Set permissions to 777 (rwxrwxrwx) to ensure write access
                uploadPath.toFile().setReadable(true, false);
                uploadPath.toFile().setWritable(true, false);
                uploadPath.toFile().setExecutable(true, false);
                log.info("Created tenant upload directory: {}", uploadPath);
            }

            // Generate a unique file name
            String originalFileName = StringUtils.cleanPath(Objects.requireNonNull(file.getOriginalFilename()));
            String fileExtension = getFileExtension(originalFileName);
            String fileName = "favicon_" + UUID.randomUUID() + "." + fileExtension;

            // Save the file
            Path targetLocation = uploadPath.resolve(fileName);
            Files.copy(file.getInputStream(), targetLocation, StandardCopyOption.REPLACE_EXISTING);

            // Get tenant domain for the CDN URL
            Optional<Tenant> tenantOpt = tenantService.findByTenantId(tenantId);
            String domain = tenantOpt.isPresent() ? tenantOpt.get().getDomain() : null;

            // Create the file URL with tenant domain if available
            String fileUrl;
            if (domain != null && !domain.isEmpty()) {
                // Use https protocol for the CDN URL with tenant domain
                fileUrl = "https://" + domain + "/cdn/" + tenantId + "/" + fileName;
                log.info("Using tenant domain for CDN URL: {}", fileUrl);
            } else {
                // Fallback to the default CDN URL
                fileUrl = cdnUrl + "/" + tenantId + "/" + fileName;
                log.info("Using default CDN URL: {}", fileUrl);
            }

            // Update the design settings
            FaviconDto faviconDto = new FaviconDto();
            faviconDto.setUrl(fileUrl);
            faviconDto.setFileType(fileExtension);
            designSettingsService.updateFavicon(tenantId, faviconDto);

            // Return the response
            return FileUploadResponse.builder()
                    .fileName(fileName)
                    .fileType(file.getContentType())
                    .url(fileUrl)
                    .size(file.getSize())
                    .build();
        } catch (IOException ex) {
            log.error("Error uploading favicon file", ex);
            throw new FileStorageException("Could not store file. Please try again!", ex);
        }
    }

    /**
     * Upload an icon file for platforms
     * @param file The icon file to upload (PNG format)
     * @param tenantId The tenant ID
     * @return The uploaded file information
     */
    @Override
    public FileUploadResponse uploadIcon(MultipartFile file, String tenantId) {
        try {
            // Validate file type - only allow PNG files
            String contentType = file.getContentType();
            if (contentType == null || !contentType.equals("image/png")) {
                throw new FileStorageException("Only PNG files are allowed for icon upload!");
            }

            // First, ensure the base upload directory exists with proper permissions
            Path baseUploadPath = Paths.get(uploadDir).toAbsolutePath().normalize();
            if (!Files.exists(baseUploadPath)) {
                try {
                    Files.createDirectories(baseUploadPath);
                    // Set permissions to 777 (rwxrwxrwx) to ensure write access
                    baseUploadPath.toFile().setReadable(true, false);
                    baseUploadPath.toFile().setWritable(true, false);
                    baseUploadPath.toFile().setExecutable(true, false);
                    log.info("Created base upload directory: {}", baseUploadPath);
                } catch (IOException e) {
                    log.error("Failed to create base upload directory: {}", baseUploadPath, e);
                    throw new FileStorageException("Could not create upload directory. Please check permissions!", e);
                }
            }

            // Create the tenant-specific directory
            String tenantDir = uploadDir + "/" + tenantId;
            Path uploadPath = Paths.get(tenantDir).toAbsolutePath().normalize();
            if (!Files.exists(uploadPath)) {
                Files.createDirectories(uploadPath);
                // Set permissions to 777 (rwxrwxrwx) to ensure write access
                uploadPath.toFile().setReadable(true, false);
                uploadPath.toFile().setWritable(true, false);
                uploadPath.toFile().setExecutable(true, false);
                log.info("Created tenant upload directory: {}", uploadPath);
            }

            // Generate a unique file name
            String originalFileName = StringUtils.cleanPath(Objects.requireNonNull(file.getOriginalFilename()));
            String fileExtension = getFileExtension(originalFileName);
            String fileName = "icon_" + UUID.randomUUID() + "." + fileExtension;

            // Save the file
            Path targetLocation = uploadPath.resolve(fileName);
            Files.copy(file.getInputStream(), targetLocation, StandardCopyOption.REPLACE_EXISTING);

            // Get tenant domain for the CDN URL
            Optional<Tenant> tenantOpt = tenantService.findByTenantId(tenantId);
            String domain = tenantOpt.map(Tenant::getDomain).orElse(null);

            // Create the file URL with tenant domain if available
            String fileUrl;
            if (domain != null && !domain.isEmpty()) {
                // Use https protocol for the CDN URL with tenant domain
                fileUrl = "https://" + domain + "/cdn/" + tenantId + "/" + fileName;
                log.info("Using tenant domain for CDN URL: {}", fileUrl);
            } else {
                // Fallback to the default CDN URL
                fileUrl = cdnUrl + "/" + tenantId + "/" + fileName;
                log.info("Using default CDN URL: {}", fileUrl);
            }

            // Return the response
            return FileUploadResponse.builder()
                    .fileName(fileName)
                    .fileType(file.getContentType())
                    .url(fileUrl)
                    .size(file.getSize())
                    .build();
        } catch (IOException ex) {
            log.error("Error uploading icon file", ex);
            throw new FileStorageException("Could not store file. Please try again!", ex);
        }
    }

    /**
     * Upload a file for chat (images only)
     * @param file The image file to upload
     * @param tenantId The tenant ID
     * @return The uploaded file information
     */
    @Override
    public FileUploadResponse uploadChatFile(MultipartFile file, String tenantId) {
        try {
            // Validate file type - only allow image files
            String contentType = file.getContentType();
            if (contentType == null || !contentType.startsWith("image/")) {
                throw new FileStorageException("Only image files are allowed for chat upload!");
            }

            // Validate file size (max 10MB)
            if (file.getSize() > 10 * 1024 * 1024) {
                throw new FileStorageException("File size must be less than 10MB!");
            }

            // First, ensure the base upload directory exists with proper permissions
            Path baseUploadPath = Paths.get(uploadDir).toAbsolutePath().normalize();
            if (!Files.exists(baseUploadPath)) {
                try {
                    Files.createDirectories(baseUploadPath);
                    // Set permissions to 777 (rwxrwxrwx) to ensure write access
                    baseUploadPath.toFile().setReadable(true, false);
                    baseUploadPath.toFile().setWritable(true, false);
                    baseUploadPath.toFile().setExecutable(true, false);
                    log.info("Created base upload directory: {}", baseUploadPath);
                } catch (IOException e) {
                    log.error("Failed to create base upload directory: {}", baseUploadPath, e);
                    throw new FileStorageException("Could not create upload directory. Please check permissions!", e);
                }
            }

            // Create tenant-specific directory
            Path uploadPath = baseUploadPath.resolve(tenantId);
            if (!Files.exists(uploadPath)) {
                try {
                    Files.createDirectories(uploadPath);
                    // Set permissions to 777 (rwxrwxrwx) to ensure write access
                    uploadPath.toFile().setReadable(true, false);
                    uploadPath.toFile().setWritable(true, false);
                    uploadPath.toFile().setExecutable(true, false);
                    log.info("Created tenant upload directory: {}", uploadPath);
                } catch (IOException e) {
                    log.error("Failed to create tenant upload directory: {}", uploadPath, e);
                    throw new FileStorageException("Could not create tenant upload directory. Please check permissions!", e);
                }
            }

            // Generate a unique file name
            String originalFileName = StringUtils.cleanPath(Objects.requireNonNull(file.getOriginalFilename()));
            String fileExtension = getFileExtension(originalFileName);
            String fileName = "chat_" + UUID.randomUUID() + "." + fileExtension;

            // Save the file
            Path targetLocation = uploadPath.resolve(fileName);
            Files.copy(file.getInputStream(), targetLocation, StandardCopyOption.REPLACE_EXISTING);

            // Get tenant domain for the CDN URL
            Optional<Tenant> tenantOpt = tenantService.findByTenantId(tenantId);
            String domain = tenantOpt.map(Tenant::getDomain).orElse(null);

            // Create the file URL with tenant domain if available
            String fileUrl;
            if (domain != null && !domain.isEmpty()) {
                // Use https protocol for the CDN URL with tenant domain
                fileUrl = "https://" + domain + "/cdn/" + tenantId + "/" + fileName;
                log.info("Using tenant domain for chat file CDN URL: {}", fileUrl);
            } else {
                // Fallback to the default CDN URL
                fileUrl = cdnUrl + "/" + tenantId + "/" + fileName;
                log.info("Using default CDN URL for chat file: {}", fileUrl);
            }

            // Return the response
            return FileUploadResponse.builder()
                    .fileName(fileName)
                    .fileType(file.getContentType())
                    .url(fileUrl)
                    .size(file.getSize())
                    .build();
        } catch (IOException ex) {
            log.error("Error uploading chat file", ex);
            throw new FileStorageException("Could not store chat file. Please try again!", ex);
        }
    }

    /**
     * Get the file extension from a file name
     * @param fileName The file name
     * @return The file extension
     */
    private String getFileExtension(String fileName) {
        if (fileName.lastIndexOf(".") != -1 && fileName.lastIndexOf(".") != 0) {
            return fileName.substring(fileName.lastIndexOf(".") + 1);
        } else {
            return "";
        }
    }
}
