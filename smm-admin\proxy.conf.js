/**
 * Angular CLI proxy configuration with additional options
 * This file provides more flexibility than the JSON version
 */

// Determine backend target based on environment
const getBackendTarget = () => {
  // Check if running in Docker (common environment variable)
  if (process.env.DOCKER_ENV || process.env.NODE_ENV === 'production') {
    // In Docker, use container name
    return 'http://smm-system:8095';
  }

  // Check for custom backend URL from environment
  if (process.env.BACKEND_URL) {
    return process.env.BACKEND_URL;
  }

  // Default to local development
  return 'http://192.168.1.2:8095';
};

const backendTarget = getBackendTarget();
console.log('Proxy backend target:', backendTarget);

module.exports = {
  '/api': {
    target: backendTarget,
    secure: false,
    changeOrigin: true,
    logLevel: 'debug',
    pathRewrite: {
      '^/api': '/api/v1'
    },
    headers: {
      'X-Tenant-ID': '0e22c37d-bfb5-4276-bd30-355fcdb39c9e',
      'X-Tenant-Domain': 'autovnfb.com'
    },
    ws: true, // Enable WebSocket for /api routes too
    // Bypass the proxy for certain requests
    bypass: function(req, res, proxyOptions) {
      // You can add logic here to bypass the proxy for certain requests if needed
      if (req.headers.accept && req.headers.accept.indexOf('html') !== -1) {
        console.log('Skipping proxy for browser request.');
        return '/index.html';
      }
    },
    // Handle proxy errors
    onError: function(err, req, res) {
      console.error('API Proxy error:', err);
      console.error('Request URL:', req.url);
      if (req.url && req.url.includes('/ws')) {
        console.error('WebSocket error on /api/ws route');
      }
    },
    // Log proxy activity
    onProxyRes: function(proxyRes, req, res) {
      console.log('Response from backend:', proxyRes.statusCode, req.url);
    },
    // WebSocket specific handlers for /api/ws
    onProxyReqWs: function(proxyReq, req, socket, options, head) {
      if (req.url && req.url.includes('/ws')) {
        console.log('WebSocket upgrade request on /api route:', req.url);
        console.log('WebSocket headers:', req.headers);
      }
    },
    onProxyResWs: function(proxyRes, req, socket, head) {
      if (req.url && req.url.includes('/ws')) {
        console.log('WebSocket upgrade response on /api route:', proxyRes.statusCode);
      }
    }
  },

  // WebSocket proxy configuration
  '/ws': {
    target: backendTarget,
    secure: false,
    changeOrigin: true,
    logLevel: 'debug',
    pathRewrite: {
      '^/ws': '/api/v1/ws'
    },
    headers: {
      'X-Tenant-ID': '0e22c37d-bfb5-4276-bd30-355fcdb39c9e',
      'X-Tenant-Domain': 'autovnfb.com'
    },
    ws: true, // Enable WebSocket proxying
    xfwd: true, // Add X-Forwarded-* headers
    onError: function(err, req, res) {
      console.error('WebSocket proxy error:', err);
      console.error('Request URL:', req.url);
      console.error('Request headers:', req.headers);
    },
    onProxyRes: function(proxyRes, req, res) {
      console.log('WebSocket response from backend:', proxyRes.statusCode, req.url);
    },
    onProxyReqWs: function(proxyReq, req, socket, options, head) {
      console.log('WebSocket upgrade request:', req.url);
      console.log('WebSocket headers:', req.headers);
    },
    onProxyResWs: function(proxyRes, req, socket, head) {
      console.log('WebSocket upgrade response:', proxyRes.statusCode);
    }
  },

  // Add additional proxy configurations if needed
  // For example, if you have a separate CDN endpoint:
  '/cdn': {
    target: 'https://autovnfb.com',
    secure: false,
    changeOrigin: true,
    logLevel: 'debug'
  }
};
