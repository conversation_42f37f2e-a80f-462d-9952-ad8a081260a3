<div class="admin-panel-container p-6">
  <!-- Header -->
  <div class="mb-6">
    <h1 class="text-2xl font-bold text-gray-900 mb-2">Admin Panel Management</h1>
    <p class="text-gray-600">Manage all panels and main tenant users</p>
  </div>

  <!-- Tab Navigation -->
  <div class="mb-6">
    <nav class="flex space-x-8" aria-label="Tabs">
      <button
        (click)="switchTab('panels')"
        [class]="activeTab === 'panels' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
        class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
        <fa-icon [icon]="['fas', 'server']" class="mr-2"></fa-icon>
        {{ 'admin.panel_management' | translate }}
      </button>
      <button
        (click)="switchTab('users')"
        [class]="activeTab === 'users' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
        class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
        <fa-icon [icon]="['fas', 'users']" class="mr-2"></fa-icon>
        {{ 'admin.user_management' | translate }}
      </button>
      <button
        (click)="switchTab('orders')"
        [class]="activeTab === 'orders' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
        class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
        <fa-icon [icon]="['fas', 'shopping-cart']" class="mr-2"></fa-icon>
        {{ 'admin.order_tracking.title' | translate }}
      </button>
      <button
        (click)="switchTab('messages')"
        [class]="activeTab === 'messages' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
        class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
        <fa-icon [icon]="['fas', 'comments']" class="mr-2"></fa-icon>
        {{ 'admin.messages' | translate }}
      </button>
    </nav>
  </div>

  <!-- Panels Tab -->
  <div *ngIf="activeTab === 'panels'" class="panels-tab">
    <app-panels-management (openDomainInfo)="openDomainInfoModal($event.tenantId, $event.domain)"></app-panels-management>
  </div>

  <!-- Users Tab -->
  <div *ngIf="activeTab === 'users'" class="users-tab">
    <app-users-management></app-users-management>
  </div>

  <!-- Loading Overlay -->
  <div *ngIf="isLoading" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg p-6 flex items-center space-x-3">
      <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
      <span class="text-gray-700">Loading...</span>
    </div>
  </div>

  <!-- Orders Tab -->
  <div *ngIf="activeTab === 'orders'" class="orders-tab">
    <app-order-tracking></app-order-tracking>
  </div>

  <!-- Messages Tab -->
  <div *ngIf="activeTab === 'messages'" class="messages-tab">
    <!-- Chat Rooms List -->
    <div *ngIf="!showChatInterface" class="bg-white rounded-lg shadow overflow-hidden">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">Support Chat Rooms</h3>
        <p class="text-sm text-gray-600 mt-1">Chat conversations with panel users</p>
      </div>

      <!-- Chat Rooms List -->
      <div class="divide-y divide-gray-200 max-h-96 overflow-y-auto">
        <div *ngFor="let chatRoom of supportChatRooms"
             class="p-6 hover:bg-gray-50 cursor-pointer transition-colors"
             (click)="openChatRoom(chatRoom)">
          <div class="flex items-start space-x-4">
            <div class="flex-shrink-0">
              <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                <fa-icon [icon]="['fas', 'user']" class="text-blue-600"></fa-icon>
              </div>
            </div>
            <div class="flex-1 min-w-0">
              <div class="flex items-center justify-between">
                <div>
                  <p class="text-sm font-medium text-gray-900">
                    {{ getUserDisplayName(chatRoom) }}
                  </p>
                  <p class="text-sm text-gray-500">
                    {{ getUserEmail(chatRoom) }}
                  </p>
                </div>
                <div class="text-sm text-gray-500">
                  {{ getLastMessageTime(chatRoom) }}
                </div>
              </div>
              <div class="mt-2">
                <p class="text-sm text-gray-600">{{ getLastMessagePreview(chatRoom) }}</p>
              </div>
              <div class="mt-2 flex items-center">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  <fa-icon [icon]="['fas', 'comments']" class="mr-1"></fa-icon>
                  Chat Room
                </span>
              </div>
            </div>
            <div class="flex-shrink-0">
              <fa-icon [icon]="['fas', 'chevron-right']" class="text-gray-400"></fa-icon>
            </div>
          </div>
        </div>

        <!-- Empty State -->
        <div *ngIf="supportChatRooms.length === 0 && !isLoading" class="p-6 text-center">
          <fa-icon [icon]="['fas', 'comments']" class="text-gray-400 text-4xl mb-4"></fa-icon>
          <h3 class="text-lg font-medium text-gray-900 mb-2">No chat rooms yet</h3>
          <p class="text-gray-600">Support chat rooms from panel users will appear here.</p>
        </div>

        <!-- Loading State -->
        <div *ngIf="isLoading" class="p-6 text-center">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p class="text-gray-600 mt-2">Loading chat rooms...</p>
        </div>
      </div>

      <!-- Refresh Button -->
      <div class="px-6 py-4 bg-gray-50 border-t border-gray-200">
        <button
          (click)="loadSupportMessages()"
          [disabled]="isLoading"
          class="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors">
          <fa-icon [icon]="['fas', 'refresh']" class="mr-2" [spin]="isLoading"></fa-icon>
          Refresh Chat Rooms
        </button>
      </div>
    </div>

    <!-- Chat Interface -->
    <div *ngIf="showChatInterface && selectedChatRoom">
      <app-support-chat
        [selectedChatRoom]="selectedChatRoom"
        (backToRooms)="onBackToRooms()">
      </app-support-chat>
    </div>
  </div>

</div>

<!-- Domain Info Modal -->
<app-domain-info
  *ngIf="showDomainInfoModal"
  [tenantId]="selectedTenantId"
  [domain]="selectedDomain"
  (close)="closeDomainInfoModal()">
</app-domain-info>


