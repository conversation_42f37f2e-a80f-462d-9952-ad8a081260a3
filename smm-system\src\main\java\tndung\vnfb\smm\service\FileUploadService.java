package tndung.vnfb.smm.service;

import org.springframework.web.multipart.MultipartFile;
import tndung.vnfb.smm.dto.response.FileUploadResponse;

/**
 * Service for handling file uploads
 */
public interface FileUploadService {
    /**
     * Upload a logo file
     * @param file The logo file to upload
     * @param tenantId The tenant ID
     * @return The uploaded file information
     */
    FileUploadResponse uploadLogo(MultipartFile file, String tenantId);

    /**
     * Upload a favicon file
     * @param file The favicon file to upload
     * @param tenantId The tenant ID
     * @return The uploaded file information
     */
    FileUploadResponse uploadFavicon(MultipartFile file, String tenantId);

    /**
     * Upload an icon file for platforms
     * @param file The icon file to upload (PNG format)
     * @param tenantId The tenant ID
     * @return The uploaded file information
     */
    FileUploadResponse uploadIcon(MultipartFile file, String tenantId);

    /**
     * Upload a file for chat (images only)
     * @param file The image file to upload
     * @param tenantId The tenant ID
     * @return The uploaded file information
     */
    FileUploadResponse uploadChatFile(MultipartFile file, String tenantId);
}
