export interface AdminPanelTenant {
  id: string;
  domain: string;
  status: string;
  apiUrl: string;
  siteUrl: string;
  contact_email: string;
  main: boolean;
  subscription_start_date: string;
  subscription_end_date: string;
  auto_renewal: boolean;
  main_currency: string;
  available_currencies: string;
  created_at: string;
  updated_at: string;
  
  // User information for the tenant owner
  owner_user_name: string;
  owner_email: string;
  total_users: number;
}
