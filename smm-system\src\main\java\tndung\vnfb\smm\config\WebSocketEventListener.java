package tndung.vnfb.smm.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.messaging.simp.stomp.StompHeaderAccessor;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.messaging.SessionConnectedEvent;
import org.springframework.web.socket.messaging.SessionDisconnectEvent;
import org.springframework.web.socket.messaging.SessionSubscribeEvent;
import org.springframework.web.socket.messaging.SessionUnsubscribeEvent;

import java.security.Principal;

@Component
@Slf4j
public class WebSocketEventListener {

    @EventListener
    public void handleWebSocketConnectListener(SessionConnectedEvent event) {
        StompHeaderAccessor headerAccessor = StompHeaderAccessor.wrap(event.getMessage());
        Principal user = headerAccessor.getUser();
        String sessionId = headerAccessor.getSessionId();

        log.info("=== WebSocket Connection Established ===");
        log.info("Session ID: {}", sessionId);
        log.info("User Principal: {}", user != null ? user.getName() : "null");
        log.info("User Principal Class: {}", user != null ? user.getClass().getSimpleName() : "null");
        log.info("Headers: {}", headerAccessor.toNativeHeaderMap());

        // Log user session mapping for debugging
        if (user != null) {
            log.info("User ID {} mapped to WebSocket session {}", user.getName(), sessionId);
        } else {
            log.warn("No user principal found for WebSocket session {}", sessionId);
        }
    }

    @EventListener
    public void handleWebSocketDisconnectListener(SessionDisconnectEvent event) {
        StompHeaderAccessor headerAccessor = StompHeaderAccessor.wrap(event.getMessage());
        Principal user = headerAccessor.getUser();
        String sessionId = headerAccessor.getSessionId();
        
        log.info("=== WebSocket Connection Closed ===");
        log.info("Session ID: {}", sessionId);
        log.info("User Principal: {}", user != null ? user.getName() : "null");
    }

    @EventListener
    public void handleWebSocketSubscribeListener(SessionSubscribeEvent event) {
        StompHeaderAccessor headerAccessor = StompHeaderAccessor.wrap(event.getMessage());
        Principal user = headerAccessor.getUser();
        String sessionId = headerAccessor.getSessionId();
        String destination = headerAccessor.getDestination();
        
        log.info("=== WebSocket Subscription ===");
        log.info("Session ID: {}", sessionId);
        log.info("User Principal: {}", user != null ? user.getName() : "null");
        log.info("Destination: {}", destination);
    }

    @EventListener
    public void handleWebSocketUnsubscribeListener(SessionUnsubscribeEvent event) {
        StompHeaderAccessor headerAccessor = StompHeaderAccessor.wrap(event.getMessage());
        Principal user = headerAccessor.getUser();
        String sessionId = headerAccessor.getSessionId();
        String subscriptionId = headerAccessor.getSubscriptionId();
        
        log.info("=== WebSocket Unsubscription ===");
        log.info("Session ID: {}", sessionId);
        log.info("User Principal: {}", user != null ? user.getName() : "null");
        log.info("Subscription ID: {}", subscriptionId);
    }
}
