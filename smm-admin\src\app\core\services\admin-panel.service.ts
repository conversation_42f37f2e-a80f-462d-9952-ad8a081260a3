import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, BehaviorSubject } from 'rxjs';
import { tap } from 'rxjs/operators';
import { ConfigService } from './config.service';
import { AdminPanelTenant } from '../../model/response/admin-panel-tenant.model';
import { AdminPanelUser } from '../../model/response/admin-panel-user.model';
import { AdminPanelUserRequest } from '../../model/request/admin-panel-user.model';
import { TransactionRes } from '../../model/response/transaction.model';

export interface PageResponse<T> {
  content: T[];
  total_elements: number;
  total_pages: number;
  page_size: number;
  page_number: number;
}

@Injectable({
  providedIn: 'root'
})
export class AdminPanelService {
  private readonly baseUrl: string;

  // State management
  private panelsSubject = new BehaviorSubject<AdminPanelTenant[]>([]);
  private usersSubject = new BehaviorSubject<AdminPanelUser[]>([]);
  private loadingSubject = new BehaviorSubject<boolean>(false);
  private panelsPaginationSubject = new BehaviorSubject<any>({
    page_number: 0,
    page_size: 10,
    total_elements: 0,
    total_pages: 0
  });
  private usersPaginationSubject = new BehaviorSubject<any>({
    page_number: 0,
    page_size: 10,
    total_elements: 0,
    total_pages: 0
  });

  // Observables
  public panels$ = this.panelsSubject.asObservable();
  public users$ = this.usersSubject.asObservable();
  public loading$ = this.loadingSubject.asObservable();
  public panelsPagination$ = this.panelsPaginationSubject.asObservable();
  public usersPagination$ = this.usersPaginationSubject.asObservable();

  constructor(
    private http: HttpClient,
    private configService: ConfigService
  ) {
    this.baseUrl = `${this.configService.apiUrl}/admin-panels`;
  }

  /**
   * Get all panels (tenants with main = false)
   */
  getAllPanels(page: number = 0, size: number = 10, search?: string): Observable<PageResponse<AdminPanelTenant>> {
    this.loadingSubject.next(true);

    let params = new HttpParams()
      .set('page', page.toString())
      .set('size', size.toString());

    if (search && search.trim()) {
      params = params.set('search', search.trim());
    }

    return this.http.get<PageResponse<AdminPanelTenant>>(`${this.baseUrl}/panels`, { params })
      .pipe(
        tap(response => {
          this.panelsSubject.next(response.content);
          this.panelsPaginationSubject.next({
            page_number: response.page_number,
            page_size: response.page_size,
            total_elements: response.total_elements,
            total_pages: response.total_pages
          });
          this.loadingSubject.next(false);
        })
      );
  }

  /**
   * Get all users from main tenant
   */
  getMainTenantUsers(page: number = 0, size: number = 10, search?: string): Observable<PageResponse<AdminPanelUser>> {
    this.loadingSubject.next(true);

    let params = new HttpParams()
      .set('page', page.toString())
      .set('size', size.toString());

    if (search && search.trim()) {
      params = params.set('search', search.trim());
    }

    return this.http.get<PageResponse<AdminPanelUser>>(`${this.baseUrl}/main-tenant-users`, { params })
      .pipe(
        tap(response => {
          this.usersSubject.next(response.content);
          this.usersPaginationSubject.next({
            page_number: response.page_number,
            page_size: response.page_size,
            total_elements: response.total_elements,
            total_pages: response.total_pages
          });
          this.loadingSubject.next(false);
        })
      );
  }

  /**
   * Add money to a main tenant user
   */
  addMoneyToUser(request: AdminPanelUserRequest): Observable<TransactionRes> {
    this.loadingSubject.next(true);

    return this.http.post<TransactionRes>(`${this.baseUrl}/add-money`, request)
      .pipe(
        tap(() => {
          this.loadingSubject.next(false);
        })
      );
  }

  /**
   * Get tenant details by ID
   */
  getTenantById(tenantId: string): Observable<AdminPanelTenant> {
    return this.http.get<AdminPanelTenant>(`${this.baseUrl}/panels/${tenantId}`);
  }

  /**
   * Get user details by ID
   */
  getUserById(userId: number): Observable<AdminPanelUser> {
    return this.http.get<AdminPanelUser>(`${this.baseUrl}/users/${userId}`);
  }

  /**
   * Get domain information by tenant ID
   */
  getDomainInfo(tenantId: string): Observable<any> {
    return this.http.get<any>(`${this.baseUrl}/panels/${tenantId}/info`);
  }

  /**
   * Get all orders from all tenants
   */
  getAllOrders(searchRequest: any): Observable<any> {
    let params = new HttpParams();

    if (searchRequest.search) {
      params = params.set('search', searchRequest.search);
    }
    if (searchRequest.tenantId) {
      params = params.set('tenantId', searchRequest.tenantId);
    }
    if (searchRequest.status) {
      params = params.set('status', searchRequest.status);
    }
    if (searchRequest.startDate) {
      params = params.set('startDate', searchRequest.startDate);
    }
    if (searchRequest.endDate) {
      params = params.set('endDate', searchRequest.endDate);
    }
    if (searchRequest.page !== undefined) {
      params = params.set('page', searchRequest.page.toString());
    }
    if (searchRequest.size !== undefined) {
      params = params.set('size', searchRequest.size.toString());
    }

    return this.http.get<any>(`${this.baseUrl}/orders`, { params });
  }

  // Getters for current values
  get panelsValue(): AdminPanelTenant[] {
    return this.panelsSubject.value;
  }

  get usersValue(): AdminPanelUser[] {
    return this.usersSubject.value;
  }

  get panelsPaginationValue(): any {
    return this.panelsPaginationSubject.value;
  }

  get usersPaginationValue(): any {
    return this.usersPaginationSubject.value;
  }
}
