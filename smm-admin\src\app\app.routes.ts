import { Routes } from '@angular/router';
// Layout components - keep these as they're used directly in routes
import { AdminLayoutComponent } from './components/admin/layout/admin-layout.component';
import { LandingPageComponent } from './components/landing-page/landing-page.component';
import { LayoutAuthComponent } from './components/layout-auth/layout-auth.component';
import { SettingsLayoutComponent } from './components/settings/layout/settings-layout.component';
import { ErrorLayoutComponent } from './components/error/error-layout.component';
import { AdminPanelComponent } from './components/admin-panel/admin-panel.component';

// Auth components - keep these as they're used directly
import { AuthComponent } from './components/auth/auth.component';
import { SignUpComponent } from './components/sign-up/sign-up.component';
import { MfaComponent } from './components/mfa/mfa.component';

// Error components - keep these as they're used directly
import { NotFoundComponent } from './components/error/not-found.component';
import { UnauthorizedComponent } from './components/error/unauthorized.component';
import { ForbiddenComponent } from './components/error/forbidden.component';
import { ServerErrorComponent } from './components/error/server-error.component';

// Route configurations
import { interactionRoutes } from './components/settings/interaction/interaction.routes';
import { promotionsRoutes } from './components/settings/promotions/promotions.routes';

// Guards
import { authGuard } from './core/guards/auth.guard';
import { adminRoleGuard } from './core/guards/admin-role.guard';
import { adminPanelRoleGuard } from './core/guards/admin-panel-role.guard';
import { authRedirectGuard } from './core/guards/auth-redirect.guard';
import { mfaGuard } from './core/guards/mfa.guard';

export const routes: Routes = [
  {
    path: '',
    pathMatch: 'full',
    component: LandingPageComponent
  },
  {
    path: 'admin',
    component: AdminPanelComponent,
    canActivate: [authGuard, adminPanelRoleGuard]
  },
  {
    path: 'panel',
    component: AdminLayoutComponent,
    canActivate: [authGuard, adminRoleGuard],
    children: [
      { path: '', pathMatch: 'full', redirectTo: 'dashboard' },
      { path: 'dashboard', loadComponent: () => import('./components/admin/dashboard/admin-dashboard.component').then(m => m.AdminDashboardComponent) },
      { path: 'orders', loadComponent: () => import('./components/admin/orders/admin-orders.component').then(m => m.AdminOrdersComponent) },
      { path: 'users', loadComponent: () => import('./components/admin/users/admin-users.component').then(m => m.AdminUsersComponent) },

      { path: 'services', loadComponent: () => import('./components/admin/service/admin-services-v2.component').then(m => m.AdminServicesV2Component) },
      // { path: 'services-v2', loadComponent: () => import('./components/admin/service/admin-services-v2.component').then(m => m.AdminServicesV2Component) },
      { path: 'tickets', loadComponent: () => import('./components/admin/support/admin-support.component').then(m => m.AdminSupportComponent) },
      { path: 'statistics', loadComponent: () => import('./components/admin/dashboard/admin-dashboard.component').then(m => m.AdminDashboardComponent) },

      // Settings routes
      {
        path: 'settings',
        component: SettingsLayoutComponent,
        children: [
          { path: '', pathMatch: 'full', redirectTo: 'general' },
          { path: 'general', loadComponent: () => import('./components/settings/general/general.component').then(m => m.GeneralComponent) },
          { path: 'providers', loadComponent: () => import('./components/settings/providers/providers.component').then(m => m.ProvidersComponent) },
          { path: 'profile', loadComponent: () => import('./components/profile/profile.component').then(m => m.ProfileComponent) },
          { path: 'interaction', children: interactionRoutes },
          { path: 'promotions', children: promotionsRoutes },
          { path: 'promo-codes', loadComponent: () => import('./components/admin/promo-codes/promo-codes.component').then(m => m.PromoCodesComponent) },
          { path: 'integrations', loadComponent: () => import('./components/settings/integrations/integrations.component').then(m => m.IntegrationsComponent) },
          { path: 'design', loadComponent: () => import('./components/settings/design/design.component').then(m => m.DesignComponent) },
          { path: 'i18n', loadComponent: () => import('./components/settings/i18n-management/i18n-management.component').then(m => m.I18nManagementComponent) },
          { path: 'currency', loadComponent: () => import('./components/settings/currency-settings-page/currency-settings-page.component').then(m => m.CurrencySettingsPageComponent) },
          { path: 'all-panels', loadComponent: () => import('./components/settings/panels-setting/panels-setting.component').then(m => m.PanelsSettingComponent) },
          { path: 'managers', loadComponent: () => import('./components/admin/managers/managers.component').then(m => m.ManagersComponent) },
        ]
      },
    ]
  },
  {
    path: 'auth',
    component: LayoutAuthComponent,
    children: [
      {
        path: 'login',
        component: AuthComponent,
        canActivate: [authRedirectGuard]
      },
      {
        path: 'register',
        component: SignUpComponent,
        canActivate: [authRedirectGuard]
      },
      {
        path: 'mfa',
        component: MfaComponent,
        canActivate: [mfaGuard]
      },
    ]
  },
  // Error routes
  {
    path: 'error',
    component: ErrorLayoutComponent,
    children: [
      { path: '404', component: NotFoundComponent },
      { path: '401', component: UnauthorizedComponent },
      { path: '403', component: ForbiddenComponent },
      { path: '502', component: ServerErrorComponent },
      { path: '503', component: ServerErrorComponent },
    ]
  },
  // Wildcard route for 404
  {
    path: '**',
    redirectTo: '/error/404'
  }
];
