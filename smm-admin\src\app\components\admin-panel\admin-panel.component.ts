import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IconsModule } from '../../icons/icons.module';
import { TranslateModule } from '@ngx-translate/core';
import { ChatService, ChatRoom } from '../../core/services/chat.service';
import { ToastService } from '../../core/services/toast.service';
import { Subscription } from 'rxjs';
import { DomainInfoComponent } from '../popup/domain-info/domain-info.component';
import { OrderTrackingComponent } from './order-tracking/order-tracking.component';
import { SupportChatComponent } from './support-chat/support-chat.component';
import { PanelsManagementComponent } from './panels-management/panels-management.component';
import { UsersManagementComponent } from './users-management/users-management.component';

@Component({
  selector: 'app-admin-panel',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    IconsModule,
    TranslateModule,
    DomainInfoComponent,
    OrderTrackingComponent,
    SupportChatComponent,
    PanelsManagementComponent,
    UsersManagementComponent
  ],
  templateUrl: './admin-panel.component.html',
  styleUrls: ['./admin-panel.component.css']
})
export class AdminPanelComponent implements OnInit, OnDestroy {
  // Tab management
  activeTab: 'panels' | 'users' | 'orders' | 'messages' = 'panels';

  // Loading state
  isLoading = false;

  // Support messages data
  supportChatRooms: ChatRoom[] = [];
  selectedChatRoom: ChatRoom | null = null;
  showChatInterface = false;

  // Domain info modal
  showDomainInfoModal = false;
  selectedTenantId = '';
  selectedDomain = '';

  private subscriptions: Subscription[] = [];

  constructor(
    private chatService: ChatService,
    private toastService: ToastService
  ) {}

  ngOnInit() {
    // Subscribe to WebSocket messages for real-time chat updates
    this.subscribeToSupportMessages();
  }

  ngOnDestroy() {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  // Tab management
  switchTab(tab: 'panels' | 'users' | 'orders' | 'messages') {
    this.activeTab = tab;
    if (tab === 'messages') {
      this.loadSupportMessages();
    }
    // Other tabs are handled by their respective components
  }

  // Domain info modal methods
  openDomainInfoModal(tenantId: string, domain: string) {
    this.selectedTenantId = tenantId;
    this.selectedDomain = domain;
    this.showDomainInfoModal = true;
  }

  closeDomainInfoModal() {
    this.showDomainInfoModal = false;
    this.selectedTenantId = '';
    this.selectedDomain = '';
  }

  // Support Messages methods
  private subscribeToSupportMessages(): void {
    // Subscribe to WebSocket messages for real-time updates
    const messagesSub = this.chatService.messages$.subscribe(_ => {
      // Refresh chat rooms when new messages arrive
      if (this.activeTab === 'messages' && !this.showChatInterface) {
        this.loadSupportMessages();
      }
    });
    this.subscriptions.push(messagesSub);
  }

  loadSupportMessages() {
    this.isLoading = true;
    this.chatService.getSupportChatRooms().subscribe({
      next: (response) => {
        console.log('Admin panel - Support chat rooms response:', response);
        if (response.content) {
          this.supportChatRooms = response.content;
        }
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading support chat rooms:', error);
        this.toastService.showError('Failed to load support chat rooms');
        this.isLoading = false;
      }
    });
  }

  openChatRoom(chatRoom: ChatRoom) {
    this.selectedChatRoom = chatRoom;
    this.showChatInterface = true;
  }

  onBackToRooms() {
    this.showChatInterface = false;
    this.selectedChatRoom = null;
    // Refresh the chat rooms list
    this.loadSupportMessages();
  }

  getUserDisplayName(chatRoom: ChatRoom): string {
    if (!chatRoom.creator) return 'Unknown User';
    return chatRoom.creator.full_name ||
           chatRoom.creator.user_name ||
           'Unknown User';
  }

  getUserEmail(chatRoom: ChatRoom): string {
    if (!chatRoom.creator) return '';
    return chatRoom.creator.email || '';
  }

  getLastMessagePreview(chatRoom: ChatRoom): string {
    if (!chatRoom.last_message) return 'No messages yet';
    const content = chatRoom.last_message.content;
    return content.length > 50 ? content.substring(0, 50) + '...' : content;
  }

  getLastMessageTime(chatRoom: ChatRoom): string {
    if (!chatRoom.last_message) return '';
    return this.formatMessageTime(chatRoom.last_message.created_at || '');
  }

  formatMessageTime(timestamp: string): string {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return date.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' });
    } else {
      return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
    }
  }
}
