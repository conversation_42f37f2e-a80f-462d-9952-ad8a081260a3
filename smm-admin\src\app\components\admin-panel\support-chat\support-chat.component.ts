import { Component, On<PERSON>nit, <PERSON><PERSON><PERSON><PERSON>, ViewChild, ElementRef, Input, Output, EventEmitter } from '@angular/core';
import { ChatService, ChatRoom, ChatMessage, ChatMessageRequest } from '../../../core/services/chat.service';
import { UserService } from '../../../core/services/user.service';
import { UserRes } from '../../../model/response/user-res.model';
import { Subscription } from 'rxjs';

import { CommonModule } from '@angular/common';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { FormsModule } from '@angular/forms';
import { IconsModule } from '../../../icons/icons.module';
import { IconName } from '@fortawesome/free-solid-svg-icons';

@Component({
  selector: 'app-support-chat',
  standalone: true,
  imports: [CommonModule, TranslateModule, FormsModule, IconsModule],
  templateUrl: './support-chat.component.html',
  styleUrls: ['./support-chat.component.scss']
})
export class SupportChatComponent implements OnInit, OnDestroy {
  @ViewChild('messagesContainer') messagesContainer!: ElementRef;
  @ViewChild('messageInput') messageInput!: ElementRef;
  @ViewChild('fileInput') fileInput!: ElementRef;
  private _selectedChatRoom: ChatRoom | null = null;

  @Input()
  set selectedChatRoom(room: ChatRoom | null) {
    console.log('🔄 Selected chat room changed:', room?.id);
    this._selectedChatRoom = room;

    // Reset notifications when switching rooms
    this.hasNewMessages = false;
    this.unreadCount = 0;
    this.isUserScrolledUp = false;

    if (room) {
      this.subscribeToRoomMessages();
      this.loadInitialMessages();
    }
  }

  get selectedChatRoom(): ChatRoom | null {
    return this._selectedChatRoom;
  }
  @Output() backToRooms = new EventEmitter<void>();

  // Chat properties
  messages: ChatMessage[] = [];
  newMessage: string = '';
  isLoading = false;
  currentUser: UserRes | undefined;

  // Pagination properties
  currentPage = 0;
  hasMoreMessages = true;
  isLoadingMore = false;

  // File upload properties
  selectedFile: File | null = null;
  isUploadingFile = false;

  // Notification properties
  hasNewMessages = false;
  unreadCount = 0;
  lastSeenMessageId: number | null = null;
  isUserScrolledUp = false;

  // Audio notification
  private notificationSound: HTMLAudioElement | null = null;

  private subscriptions: Subscription[] = [];

  constructor(
    private chatService: ChatService,
    private userService: UserService,
    private translateService: TranslateService
  ) {}

  ngOnInit(): void {
    this.loadCurrentUser();

    // Debug WebSocket connection
    this.debugWebSocketConnection();

    // Initialize notification sound
    this.initializeNotificationSound();

    // selectedChatRoom logic is handled by the setter
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  private loadCurrentUser(): void {
    const userSub = this.userService.user$.subscribe(user => {
      this.currentUser = user;
    });
    this.subscriptions.push(userSub);
  }

  private subscribeToRoomMessages(): void {
    if (!this.selectedChatRoom) return;

    let isFirstLoad = true;

    // Subscribe to room-specific messages observable for real-time updates
    const roomMessagesSub = this.chatService.getRoomMessages$(this.selectedChatRoom.id).subscribe(messages => {
      console.log('=== Support Chat Room Messages Update ===');
      console.log('Room ID:', this.selectedChatRoom?.id);
      console.log('Messages count:', messages.length);
      console.log('Is first load:', isFirstLoad);

      const previousCount = this.messages.length;
      this.messages = messages;

      // Check for new messages from users (not admin) - only after first load
      if (messages.length > previousCount && previousCount > 0 && !isFirstLoad) {
        console.log('🔔 NEW MESSAGES DETECTED in Support Chat!');
        console.log('Previous count:', previousCount, 'New count:', messages.length);
        const newMessages = messages.slice(previousCount);
        console.log('New messages:', newMessages);
        this.handleNewMessages(newMessages);
      }

      // Scroll to bottom logic
      if (isFirstLoad && messages.length > 0) {
        // Initial load - always scroll to bottom
        console.log('Initial load: scrolling to bottom');
        setTimeout(() => this.forceScrollToBottom(), 200);
        isFirstLoad = false;
      } else if (!isFirstLoad) {
        // New messages - smart scroll only if user is near bottom
        console.log('New messages received - checking scroll position');
        console.log('Is user scrolled up:', this.isUserScrolledUp);

        if (!this.isUserScrolledUp) {
          console.log('User at bottom - auto scrolling');
          this.scrollToBottom();
        } else {
          console.log('User scrolled up - NOT auto scrolling, keeping indicator visible');
        }
      }
    });
    this.subscriptions.push(roomMessagesSub);
  }

  private loadInitialMessages(): void {
    if (!this.selectedChatRoom) return;

    this.isLoading = true;
    this.chatService.loadRoomMessages(this.selectedChatRoom.id, 0, 50).subscribe({
      next: (response) => {
        console.log('Initial room messages loaded:', response);
        this.currentPage = 0;
        this.hasMoreMessages = response.content && response.content.length === 50;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading initial room messages:', error);
        this.isLoading = false;
      }
    });
  }

  loadChatMessages(): void {
    // This method is now mainly for manual refresh if needed
    // Real-time updates are handled by subscribeToRoomMessages()
    if (!this.selectedChatRoom) return;

    this.isLoading = true;
    this.chatService.loadRoomMessages(this.selectedChatRoom.id).subscribe({
      next: (response) => {
        console.log('Support chat messages refreshed:', response);
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error refreshing support chat messages:', error);
        this.isLoading = false;
      }
    });
  }

  sendReply(): void {
    if (!this.newMessage.trim() && !this.selectedFile || !this.selectedChatRoom) {
      return;
    }

    console.log('=== Sending Support Reply ===');
    console.log('Message content:', this.newMessage.trim());
    console.log('Selected file:', this.selectedFile);

    this.isLoading = true;

    if (this.selectedFile) {
      this.sendFileReply();
    } else {
      this.sendTextReply();
    }
  }

  private sendTextReply(): void {
    if (!this.selectedChatRoom) return;

    const messageRequest: ChatMessageRequest = {
      content: this.newMessage.trim(),
      message_type: 'TEXT'
    };

    const sub = this.chatService.sendSupportReply(this.selectedChatRoom.created_by, messageRequest).subscribe({
      next: () => {
        this.newMessage = '';
        this.isLoading = false;
        // Force scroll to bottom when sending message
        this.forceScrollToBottom();
        // Clear any notifications since admin is actively replying
        this.markMessagesAsRead();
      },
      error: (error) => {
        console.error('Error sending support reply:', error);
        this.isLoading = false;
      }
    });
    this.subscriptions.push(sub);
  }

  private sendFileReply(): void {
    if (!this.selectedFile || !this.selectedChatRoom) return;

    this.isUploadingFile = true;

    // First upload the file
    this.chatService.uploadFile(this.selectedFile).subscribe({
      next: (uploadResponse: any) => {
        console.log('File uploaded successfully:', uploadResponse);

        // Then send reply with image info
        const messageRequest: ChatMessageRequest = {
          content: this.newMessage.trim() || 'Sent an image',
          message_type: 'IMAGE', // Only images allowed
          file_url: uploadResponse.url, // Use 'url' from response
          file_name: this.selectedFile!.name,
          file_size: this.selectedFile!.size
        };

        const sub = this.chatService.sendSupportReply(this.selectedChatRoom!.created_by, messageRequest).subscribe({
          next: () => {
            console.log('Image reply sent successfully');
            this.newMessage = '';
            this.selectedFile = null;
            this.isLoading = false;
            this.isUploadingFile = false;
            // Force scroll to bottom when sending image
            this.forceScrollToBottom();
            // Clear any notifications since admin is actively replying
            this.markMessagesAsRead();
          },
          error: (error: any) => {
            console.error('Error sending image reply:', error);
            this.isLoading = false;
            this.isUploadingFile = false;
          }
        });
        this.subscriptions.push(sub);
      },
      error: (error: any) => {
        console.error('Error uploading file:', error);
        this.isLoading = false;
        this.isUploadingFile = false;
      }
    });
  }

  onKeyPress(event: KeyboardEvent): void {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      this.sendReply();
    }
  }

  private scrollToBottom(force: boolean = false): void {
    setTimeout(() => {
      if (this.messagesContainer) {
        const element = this.messagesContainer.nativeElement;

        // Check if user is near bottom (within 100px) or force scroll
        const isNearBottom = element.scrollHeight - element.scrollTop - element.clientHeight < 100;

        if (force || isNearBottom) {
          // Smooth scroll to bottom
          element.scrollTo({
            top: element.scrollHeight,
            behavior: 'smooth'
          });
        }
      }
    }, 100);
  }



  formatMessageTime(timestamp: string): string {
    if (!timestamp) return '';
    const date = new Date(timestamp);
    return date.toLocaleString();
  }

  getCurrentUserId(): number {
    return this.currentUser?.id || 0;
  }

  onBackToRooms(): void {
    this.backToRooms.emit();
  }

  getUserDisplayName(): string {
    if (!this.selectedChatRoom?.creator) return 'Unknown User';
    return this.selectedChatRoom.creator.fullName || 
           this.selectedChatRoom.creator.full_name || 
           this.selectedChatRoom.creator.userName || 
           this.selectedChatRoom.creator.user_name || 
           'Unknown User';
  }

  getUserEmail(): string {
    if (!this.selectedChatRoom?.creator) return '';
    return this.selectedChatRoom.creator.email || '';
  }

  private debugWebSocketConnection(): void {
    console.log('=== Support Chat Component - WebSocket Debug ===');
    console.log('WebSocket Info:', this.chatService.getWebSocketInfo());
    console.log('Current User:', this.currentUser);
    console.log('Selected Chat Room:', this.selectedChatRoom);

    // Check connection status every 5 seconds
    setInterval(() => {
      const wsInfo = this.chatService.getWebSocketInfo();
      if (!wsInfo.connected) {
        console.warn('WebSocket not connected:', wsInfo);
      }
    }, 5000);
  }

  // Method to manually test WebSocket
  testWebSocketConnection(): void {
    console.log('=== Testing WebSocket Connection ===');
    const wsInfo = this.chatService.getWebSocketInfo();
    console.log('WebSocket Info:', wsInfo);

    if (!wsInfo.connected) {
      console.log('WebSocket not connected, attempting reconnection...');
      this.chatService.reconnectWebSocket();
    } else {
      console.log('WebSocket is connected successfully!');

      // Test subscription by checking if we can receive messages
      console.log('Testing message reception...');
      console.log('Current support messages count:', this.messages.length);
      console.log('Support messages observable:', this.chatService.supportMessages$);

      // Force refresh messages to compare with WebSocket
      console.log('Manually refreshing messages for comparison...');
      this.loadChatMessages();
    }
  }

  // Force reconnect WebSocket for testing
  forceReconnectWebSocket(): void {
    console.log('=== Force Reconnecting WebSocket ===');
    this.chatService.forceReconnectWebSocket();

    // Wait a moment then reload messages
    setTimeout(() => {
      this.loadInitialMessages();
    }, 2000);
  }

  // Test WebSocket with detailed info
  testWebSocketDetailed(): void {
    console.log('=== Detailed WebSocket Test ===');
    this.chatService.testWebSocketConnection();
  }

  // File handling methods
  onFileSelected(event: any): void {
    const file = event.target.files[0];
    if (file) {
      // Check file type - only images allowed
      if (!file.type.startsWith('image/')) {
        this.translateService.get('chat.only_images_allowed').subscribe(message => {
          alert(message || 'Only image files are allowed');
        });
        return;
      }

      // Check file size (max 10MB)
      if (file.size > 10 * 1024 * 1024) {
        this.translateService.get('chat.file_too_large').subscribe(message => {
          alert(message || 'File size must be less than 10MB');
        });
        return;
      }

      this.selectedFile = file;
      console.log('Image selected:', file);
    }
  }

  removeSelectedFile(): void {
    this.selectedFile = null;
    if (this.fileInput) {
      this.fileInput.nativeElement.value = '';
    }
  }

  triggerFileInput(): void {
    this.fileInput.nativeElement.click();
  }

  // Scroll handling for load more and notification tracking
  onScroll(event: any): void {
    const element = event.target;

    // Load more messages when scrolled to top
    if (element.scrollTop === 0 && this.hasMoreMessages && !this.isLoadingMore) {
      this.loadMoreMessages();
    }

    // Track if user is scrolled up (not at bottom)
    const isAtBottom = element.scrollHeight - element.scrollTop - element.clientHeight < 50;
    const wasScrolledUp = this.isUserScrolledUp;
    this.isUserScrolledUp = !isAtBottom;

    // Debug scroll position with more details
    if (wasScrolledUp !== this.isUserScrolledUp) {
      console.log('📍 === SCROLL POSITION CHANGED (Support Chat) ===');
      console.log('- scrollTop:', element.scrollTop);
      console.log('- scrollHeight:', element.scrollHeight);
      console.log('- clientHeight:', element.clientHeight);
      console.log('- Distance from bottom:', element.scrollHeight - element.scrollTop - element.clientHeight);
      console.log('- isAtBottom:', isAtBottom);
      console.log('- isUserScrolledUp:', this.isUserScrolledUp);
      console.log('- hasNewMessages:', this.hasNewMessages);
      console.log('- Should show indicator:', this.hasNewMessages && this.isUserScrolledUp);
    }

    // Clear notifications if user scrolled to bottom
    if (isAtBottom && this.hasNewMessages) {
      console.log('✅ User scrolled to bottom - clearing notifications');
      this.markMessagesAsRead();
    }
  }

  private loadMoreMessages(): void {
    if (!this.selectedChatRoom || this.isLoadingMore || !this.hasMoreMessages) return;

    this.isLoadingMore = true;
    const nextPage = this.currentPage + 1;

    this.chatService.loadRoomMessages(this.selectedChatRoom.id, nextPage, 50).subscribe({
      next: (response) => {
        console.log(`Loaded page ${nextPage} messages:`, response);

        if (response.content && response.content.length > 0) {
          this.currentPage = nextPage;
          this.hasMoreMessages = response.content.length === 50;
        } else {
          this.hasMoreMessages = false;
        }

        this.isLoadingMore = false;
      },
      error: (error) => {
        console.error('Error loading more messages:', error);
        this.isLoadingMore = false;
      }
    });
  }

  // Message type checking
  isImageMessage(message: ChatMessage): boolean {
    return message.message_type === 'IMAGE' && !!message.file_url;
  }

  isFileMessage(message: ChatMessage): boolean {
    return message.message_type === 'FILE' && !!message.file_url;
  }

  getFileIcon(fileName: string): IconName {
    const extension = fileName.split('.').pop()?.toLowerCase();
    switch (extension) {
      case 'pdf': return 'file-pdf';
      case 'doc':
      case 'docx': return 'file-word';
      case 'xls':
      case 'xlsx': return 'file-excel';
      case 'ppt':
      case 'pptx': return 'file-powerpoint';
      case 'txt': return 'file-alt';
      default: return 'file';
    }
  }

  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  downloadFile(fileUrl: string, fileName: string): void {
    const link = document.createElement('a');
    link.href = fileUrl;
    link.download = fileName;
    link.target = '_blank';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }

  // Get image preview URL for selected file
  getImagePreview(file: File): string {
    return URL.createObjectURL(file);
  }

  // Initialize notification sound
  private initializeNotificationSound(): void {
    try {
      // Create notification sound
      this.notificationSound = new Audio();
      this.notificationSound.src = 'data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT';
      this.notificationSound.volume = 0.5;
      this.notificationSound.load();
    } catch (error) {
      console.warn('Could not initialize notification sound:', error);
    }
  }

  // Handle new messages (notifications, sound, etc.)
  private handleNewMessages(newMessages: ChatMessage[]): void {
    console.log('🔔 === HANDLE NEW MESSAGES (Support Chat) ===');
    console.log('New messages received:', newMessages.length);
    console.log('Current user ID:', this.currentUser?.id);

    if (!newMessages || newMessages.length === 0) {
      console.log('❌ No new messages to handle');
      return;
    }

    // Check if any new message is from a user (not admin)
    const messagesFromUsers = newMessages.filter(msg => {
      console.log(`Message from sender ${msg.sender_id}, current user: ${this.currentUser?.id}`);
      return msg.sender_id !== this.currentUser?.id;
    });

    console.log('Messages from users:', messagesFromUsers.length);

    if (messagesFromUsers.length > 0) {
      console.log('📢 Processing messages from users:', messagesFromUsers.length);
      console.log('Current state before update:');
      console.log('- Is user scrolled up:', this.isUserScrolledUp);
      console.log('- Current unread count:', this.unreadCount);
      console.log('- Has new messages:', this.hasNewMessages);

      // Always update unread count for messages from users
      this.unreadCount += messagesFromUsers.length;
      this.hasNewMessages = true;

      console.log('📊 Updated state:');
      console.log('- Updated unread count:', this.unreadCount);
      console.log('- Has new messages:', this.hasNewMessages);
      console.log('- Should show indicator:', this.hasNewMessages && this.isUserScrolledUp);

      // Play notification sound for admin
      console.log('🔊 Playing notification sound');
      this.playNotificationSound();

      // Show browser notification
      console.log('🖥️ Showing browser notification');
      this.showBrowserNotification(messagesFromUsers[0]);
    } else {
      console.log('ℹ️ All new messages are from current admin - no notifications needed');
    }
  }

  // Play notification sound
  private playNotificationSound(): void {
    try {
      if (this.notificationSound) {
        this.notificationSound.currentTime = 0;
        this.notificationSound.play().catch(error => {
          console.warn('Could not play notification sound:', error);
        });
      }
    } catch (error) {
      console.warn('Error playing notification sound:', error);
    }
  }

  // Show browser notification
  private showBrowserNotification(message: ChatMessage): void {
    if ('Notification' in window) {
      if (Notification.permission === 'granted') {
        const senderName = message.sender?.full_name || message.sender?.user_name || 'User';
        const content = message.message_type === 'IMAGE' ? 'Sent an image' : message.content;

        const notification = new Notification(`New support message from ${senderName}`, {
          body: content,
          icon: '/assets/icons/chat-icon.png',
          tag: 'support-chat-message',
          requireInteraction: false
        });

        // Auto close after 5 seconds
        setTimeout(() => notification.close(), 5000);

        // Click to focus chat
        notification.onclick = () => {
          window.focus();
          notification.close();
        };
      } else if (Notification.permission !== 'denied') {
        // Request permission
        Notification.requestPermission().then(permission => {
          if (permission === 'granted') {
            this.showBrowserNotification(message);
          }
        });
      }
    }
  }

  // Mark messages as read and clear notifications
  private markMessagesAsRead(): void {
    this.hasNewMessages = false;
    this.unreadCount = 0;

    // Update last seen message ID
    if (this.messages.length > 0) {
      this.lastSeenMessageId = this.messages[this.messages.length - 1].id;
    }
  }

  // Scroll to new messages when clicking indicator
  scrollToNewMessages(): void {
    console.log('🚀 === CLICKING NEW MESSAGES INDICATOR (Support Chat) ===');
    console.log('Current unread count:', this.unreadCount);
    console.log('Has new messages:', this.hasNewMessages);
    console.log('Is user scrolled up:', this.isUserScrolledUp);
    console.log('Messages container:', this.messagesContainer);

    if (this.messagesContainer) {
      const element = this.messagesContainer.nativeElement;
      console.log('Before scroll - scrollTop:', element.scrollTop, 'scrollHeight:', element.scrollHeight);

      // Try multiple scroll methods
      element.scrollTop = element.scrollHeight;

      // Also try scrollTo
      element.scrollTo({
        top: element.scrollHeight,
        behavior: 'auto'
      });

      // Force with setTimeout
      setTimeout(() => {
        element.scrollTop = element.scrollHeight;
        console.log('After timeout scroll - scrollTop:', element.scrollTop, 'scrollHeight:', element.scrollHeight);
      }, 10);

      console.log('After immediate scroll - scrollTop:', element.scrollTop, 'scrollHeight:', element.scrollHeight);

      // Update state immediately
      this.isUserScrolledUp = false;
      this.markMessagesAsRead();

      console.log('✅ Scroll completed and notifications cleared');
      console.log('Final state - isUserScrolledUp:', this.isUserScrolledUp, 'hasNewMessages:', this.hasNewMessages);
    } else {
      console.error('❌ Messages container not found!');
    }
  }

  // Force scroll to bottom (used when sending messages)
  private forceScrollToBottom(): void {
    console.log('🚀 Force scrolling to bottom (Support Chat)...');
    if (this.messagesContainer) {
      const element = this.messagesContainer.nativeElement;
      console.log('Before force scroll:');
      console.log('- scrollTop:', element.scrollTop);
      console.log('- scrollHeight:', element.scrollHeight);
      console.log('- clientHeight:', element.clientHeight);

      // Try multiple methods to ensure scroll works
      element.scrollTop = element.scrollHeight;

      // Also try scrollTo method
      element.scrollTo({
        top: element.scrollHeight,
        behavior: 'auto' // Use 'auto' instead of 'smooth' for immediate scroll
      });

      console.log('After force scroll:');
      console.log('- scrollTop:', element.scrollTop);
      console.log('- scrollHeight:', element.scrollHeight);

      // Update scroll tracking
      this.isUserScrolledUp = false;
      console.log('✅ Force scroll completed - isUserScrolledUp:', this.isUserScrolledUp);
    } else {
      console.error('❌ Messages container not found for force scroll');
    }
  }
}
