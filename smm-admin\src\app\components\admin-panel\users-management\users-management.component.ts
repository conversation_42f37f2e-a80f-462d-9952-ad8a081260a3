import { Component, OnInit, OnDestroy, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IconsModule } from '../../../icons/icons.module';
import { TranslateModule } from '@ngx-translate/core';
import { AdminPanelService } from '../../../core/services/admin-panel.service';
import { AdminPanelUser } from '../../../model/response/admin-panel-user.model';
import { AdminPanelUserRequest } from '../../../model/request/admin-panel-user.model';
import { ToastService } from '../../../core/services/toast.service';
import { CurrencyService } from '../../../core/services/currency.service';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-users-management',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    IconsModule,
    TranslateModule
  ],
  templateUrl: './users-management.component.html',
  styleUrls: ['./users-management.component.css']
})
export class UsersManagementComponent implements OnInit, OnDestroy {
  // Make Math available in template
  Math = Math;

  // Users data
  users: AdminPanelUser[] = [];
  searchTerm: string = '';
  pagination = {
    page_number: 0,
    page_size: 10,
    total_elements: 0,
    total_pages: 0
  };

  // Loading state
  isLoading = false;

  // Add money modal
  showAddMoneyModal = false;
  selectedUser: AdminPanelUser | null = null;
  addMoneyForm = {
    amount: '',
    note: ''
  };

  private subscriptions: Subscription[] = [];

  constructor(
    private adminPanelService: AdminPanelService,
    private toastService: ToastService,
    private currencyService: CurrencyService
  ) {}

  ngOnInit() {
    // Subscribe to users data
    this.subscriptions.push(
      this.adminPanelService.users$.subscribe(users => {
        this.users = users;
      })
    );

    // Subscribe to loading state
    this.subscriptions.push(
      this.adminPanelService.loading$.subscribe(loading => {
        this.isLoading = loading;
      })
    );

    // Subscribe to users pagination
    this.subscriptions.push(
      this.adminPanelService.usersPagination$.subscribe(pagination => {
        this.pagination = pagination;
      })
    );

    // Load initial data
    this.loadUsers();
  }

  ngOnDestroy() {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  // Users methods
  loadUsers(page: number = 0) {
    this.adminPanelService.getMainTenantUsers(page, this.pagination.page_size, this.searchTerm)
      .subscribe({
        error: (error) => {
          console.error('Error loading users:', error);
          this.toastService.showError('Failed to load users');
        }
      });
  }

  search() {
    this.loadUsers(0);
  }

  resetSearch() {
    this.searchTerm = '';
    this.loadUsers(0);
  }

  // Pagination methods
  loadPage(page: number) {
    this.loadUsers(page);
  }

  // Add money functionality
  openAddMoneyModal(user: AdminPanelUser) {
    this.selectedUser = user;
    this.addMoneyForm = {
      amount: '',
      note: ''
    };
    this.showAddMoneyModal = true;
  }

  closeAddMoneyModal() {
    this.showAddMoneyModal = false;
    this.selectedUser = null;
    this.addMoneyForm = {
      amount: '',
      note: ''
    };
  }

  addMoney() {
    if (!this.selectedUser || !this.addMoneyForm.amount) {
      this.toastService.showError('Please enter a valid amount');
      return;
    }

    const amount = parseFloat(this.addMoneyForm.amount);
    if (isNaN(amount) || amount <= 0) {
      this.toastService.showError('Please enter a valid positive amount');
      return;
    }

    const request: AdminPanelUserRequest = {
      user_id: this.selectedUser.id,
      amount: amount,
      source: 'BONUS',
      note: this.addMoneyForm.note || `Added by admin panel for user ${this.selectedUser.user_name}`
    };

    this.adminPanelService.addMoneyToUser(request).subscribe({
      next: (_) => {
        this.toastService.showSuccess(`Successfully added $${amount} to ${this.selectedUser!.user_name}`);
        this.closeAddMoneyModal();
        // Refresh users list
        this.loadUsers(this.pagination.page_number);
      },
      error: (error) => {
        console.error('Error adding money:', error);
        this.toastService.showError('Failed to add money to user');
      }
    });
  }

  // Utility methods
  formatBalance(balance: number | undefined): string {
    return '$' + this.currencyService.formatBalance(balance);
  }

  formatDate(dateString: string): string {
    if (!dateString) return '-';
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
  }

  getStatusClass(status: string): string {
    switch (status?.toLowerCase()) {
      case 'active':
      case 'activated':
        return 'bg-green-100 text-green-800';
      case 'suspended':
      case 'deactivated':
        return 'bg-red-100 text-red-800';
      case 'expired':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  }
}
