package tndung.vnfb.smm.repository.tenant;

import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import tndung.vnfb.smm.constant.enums.TransactionType;
import tndung.vnfb.smm.entity.GOrder;
import tndung.vnfb.smm.entity.GTransaction;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.List;
import java.util.Optional;

public interface TransactionRepository extends TenantAwareRepository<GTransaction, Long> {
    @Query("SELECT t FROM GTransaction t WHERE t.order = :order AND t.type = :type AND t.tenantId = :#{T(tndung.vnfb.smm.config.TenantContext).getWildcardTenant()}")
    Optional<GTransaction> findByOrderAndType(@Param("order") GOrder order, @Param("type") TransactionType type);

    @Query("SELECT t FROM GTransaction t WHERE t.order = :order AND t.type = :type AND t.tenantId = :#{T(tndung.vnfb.smm.config.TenantContext).getWildcardTenant()} ORDER BY t.createdAt ASC")
    List<GTransaction> findAllByOrderAndType(@Param("order") GOrder order, @Param("type") TransactionType type);

    @Query("SELECT t FROM GTransaction t WHERE t.order = :order AND t.type IN :types AND t.tenantId = :#{T(tndung.vnfb.smm.config.TenantContext).getWildcardTenant()} ORDER BY t.createdAt ASC")
    List<GTransaction> findAllByOrderAndTypeIn(@Param("order") GOrder order, @Param("types") List<TransactionType> types);

    @Query("SELECT SUM(t.change) FROM GTransaction t WHERE t.type = 'Deposit' AND t.createdAt BETWEEN :startDate AND :endDate" +
            "  AND t.tenantId = :#{T(tndung.vnfb.smm.config.TenantContext).getWildcardTenant()} ")
    BigDecimal sumRevenueBetweenDates(@Param("startDate") OffsetDateTime startDate, @Param("endDate") OffsetDateTime endDate);

    @Query("SELECT t FROM GTransaction t WHERE t.userId = :userId AND t.tenantId = :#{T(tndung.vnfb.smm.config.TenantContext).getWildcardTenant()} ORDER BY t.createdAt DESC, t.id DESC")
    List<GTransaction> findTopByUserIdOrderByCreatedAtDesc(@Param("userId") Long userId, Pageable pageable);

    // Alternative method to get the latest transaction safely
    @Query(value = "SELECT * FROM g_transaction WHERE user_id = :userId AND tenant_id = :#{T(tndung.vnfb.smm.config.TenantContext).getWildcardTenant()} ORDER BY created_at DESC, id DESC LIMIT 1", nativeQuery = true)
    Optional<GTransaction> findLatestByUserId(@Param("userId") Long userId);
    /**
     * Calculate revenue for a specific tenant ID
     */
    @Query("SELECT COALESCE(SUM(t.change), 0) FROM GTransaction t WHERE t.tenantId = :tenantId AND t.type = 'Deposit' AND t.createdAt >= :startDate AND t.createdAt <= :endDate")
    BigDecimal calculateRevenueByTenantId(@Param("tenantId") String tenantId, @Param("startDate") OffsetDateTime startDate, @Param("endDate") OffsetDateTime endDate);

    /**
     * Calculate revenue for a specific tenant ID (default to last year)
     */
    default BigDecimal calculateRevenueByTenantId(String tenantId) {
        return calculateRevenueByTenantId(tenantId, OffsetDateTime.now().minusYears(1), OffsetDateTime.now());
    }
}
